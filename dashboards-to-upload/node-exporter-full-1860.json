{"__inputs": [{"name": "DS_PROMETHEUS", "label": "prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "panel", "id": "bargauge", "name": "Bar gauge", "version": ""}, {"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "11.6.1"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [{"icon": "external link", "tags": [], "targetBlank": true, "title": "GitHub", "type": "link", "url": "https://github.com/rfmoz/grafana-dashboards"}, {"icon": "external link", "tags": [], "targetBlank": true, "title": "<PERSON><PERSON>", "type": "link", "url": "https://grafana.com/grafana/dashboards/1860"}], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 261, "panels": [], "title": "Quick CPU / Mem / Disk", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Resource pressure via PSI", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "links": [], "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "dark-yellow", "value": 70}, {"color": "dark-red", "value": 90}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 1}, "id": 323, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "irate(node_pressure_cpu_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "CPU", "range": false, "refId": "A", "step": 240}, {"editorMode": "code", "exemplar": false, "expr": "irate(node_pressure_memory_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "<PERSON><PERSON>", "range": false, "refId": "B", "step": 240}, {"editorMode": "code", "exemplar": false, "expr": "irate(node_pressure_io_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "I/O", "range": false, "refId": "C", "step": 240}, {"editorMode": "code", "exemplar": false, "expr": "irate(node_pressure_irq_stalled_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "<PERSON><PERSON><PERSON>", "range": false, "refId": "D", "step": 240}], "title": "Pressure", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Overall CPU busy percentage (averaged across all cores)", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 85}, {"color": "rgba(245, 54, 54, 0.9)", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 1}, "id": 20, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "100 * (1 - avg(rate(node_cpu_seconds_total{mode=\"idle\", instance=\"$node\"}[$__rate_interval])))", "hide": false, "instant": true, "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A", "step": 240}], "title": "CPU Busy", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "System load  over all CPU cores together", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 85}, {"color": "rgba(245, 54, 54, 0.9)", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 1}, "id": 155, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "scalar(node_load1{instance=\"$node\",job=\"$job\"}) * 100 / count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu))", "format": "time_series", "hide": false, "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "Sys Load", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Real RAM usage excluding cache and reclaimable memory", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 1}, "id": 16, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "(1 - (node_memory_MemAvailable_bytes{instance=\"$node\", job=\"$job\"} / node_memory_MemTotal_bytes{instance=\"$node\", job=\"$job\"})) * 100", "format": "time_series", "hide": false, "instant": true, "intervalFactor": 1, "range": false, "refId": "B", "step": 240}], "title": "RAM Used", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Percentage of swap space currently used by the system", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(245, 54, 54, 0.9)", "value": 25}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 1}, "id": 21, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "((node_memory_SwapTotal_bytes{instance=\"$node\",job=\"$job\"} - node_memory_SwapFree_bytes{instance=\"$node\",job=\"$job\"}) / (node_memory_SwapTotal_bytes{instance=\"$node\",job=\"$job\"})) * 100", "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "SWAP Used", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Used Root FS", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 80}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 15, "y": 1}, "id": 154, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "(\n  (node_filesystem_size_bytes{instance=\"$node\", job=\"$job\", mountpoint=\"/\", fstype!=\"rootfs\"}\n   - node_filesystem_avail_bytes{instance=\"$node\", job=\"$job\", mountpoint=\"/\", fstype!=\"rootfs\"})\n  / node_filesystem_size_bytes{instance=\"$node\", job=\"$job\", mountpoint=\"/\", fstype!=\"rootfs\"}\n) * 100\n", "format": "time_series", "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "Root FS Used", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 18, "y": 1}, "id": 14, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu))", "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}], "title": "CPU Cores", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bool_yes_no"}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 20, "y": 1}, "id": 328, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "node_reboot_required{instance=\"$node\",job=\"$job\"}", "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "Reboot Required", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 22, "y": 1}, "id": 15, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "node_time_seconds{instance=\"$node\",job=\"$job\"} - node_boot_time_seconds{instance=\"$node\",job=\"$job\"}", "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)"}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 18, "y": 3}, "id": 23, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "node_filesystem_size_bytes{instance=\"$node\",job=\"$job\",mountpoint=\"/\",fstype!=\"rootfs\"}", "format": "time_series", "hide": false, "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "RootFS Total", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 20, "y": 3}, "id": 75, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "node_memory_MemTotal_bytes{instance=\"$node\",job=\"$job\"}", "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "RAM Total", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 2, "w": 2, "x": 22, "y": 3}, "id": 18, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "node_memory_SwapTotal_bytes{instance=\"$node\",job=\"$job\"}", "instant": true, "intervalFactor": 1, "range": false, "refId": "A", "step": 240}], "title": "SWAP Total", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 263, "panels": [], "title": "Basic CPU / Mem / Net / Disk", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "CPU time spent busy vs idle, split by activity type", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "percent"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Idle"}, "properties": [{"id": "color", "value": {"fixedColor": "#052B51", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Busy System"}, "properties": [{"id": "color", "value": {"fixedColor": "#EAB839", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Busy User"}, "properties": [{"id": "color", "value": {"fixedColor": "#0A437C", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Busy Other"}, "properties": [{"id": "color", "value": {"fixedColor": "#6D1F62", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 6}, "id": 77, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true, "width": 250}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "exemplar": false, "expr": "sum(irate(node_cpu_seconds_total{instance=\"$node\",job=\"$job\", mode=\"system\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "Busy System", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{instance=\"$node\",job=\"$job\", mode=\"user\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Busy User", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{instance=\"$node\",job=\"$job\", mode=\"iowait\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON>", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{instance=\"$node\",job=\"$job\", mode=~\".*irq\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Busy IRQs", "range": true, "refId": "D", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{instance=\"$node\",job=\"$job\",  mode!='idle',mode!='user',mode!='system',mode!='iowait',mode!='irq',mode!='softirq'}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Busy Other", "range": true, "refId": "E", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{instance=\"$node\",job=\"$job\", mode=\"idle\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Idle", "range": true, "refId": "F", "step": 240}], "title": "CPU Basic", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "RAM and swap usage overview, including caches", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Swap used"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total"}, "properties": [{"id": "color", "value": {"fixedColor": "#E0F9D7", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.stacking", "value": {"group": false, "mode": "normal"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON> + <PERSON><PERSON>er"}, "properties": [{"id": "color", "value": {"fixedColor": "#052B51", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Free"}, "properties": [{"id": "color", "value": {"fixedColor": "#7EB26D", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 6}, "id": 78, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_MemTotal_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Total", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_MemTotal_bytes{instance=\"$node\",job=\"$job\"} - node_memory_MemFree_bytes{instance=\"$node\",job=\"$job\"} - (node_memory_Cached_bytes{instance=\"$node\",job=\"$job\"} + node_memory_Buffers_bytes{instance=\"$node\",job=\"$job\"} + node_memory_SReclaimable_bytes{instance=\"$node\",job=\"$job\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Used", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_Cached_bytes{instance=\"$node\",job=\"$job\"} + node_memory_Buffers_bytes{instance=\"$node\",job=\"$job\"} + node_memory_SReclaimable_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON> + <PERSON><PERSON>er", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_memory_MemFree_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Free", "range": true, "refId": "D", "step": 240}, {"editorMode": "code", "expr": "(node_memory_SwapTotal_bytes{instance=\"$node\",job=\"$job\"} - node_memory_SwapFree_bytes{instance=\"$node\",job=\"$job\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "Swap used", "range": true, "refId": "E", "step": 240}], "title": "Memory Basic", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Per-interface network traffic (receive and transmit) in bits per second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Tx.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 13}, "id": 74, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])*8", "format": "time_series", "intervalFactor": 1, "legendFormat": "Rx {{device}}", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "rate(node_network_transmit_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])*8", "format": "time_series", "intervalFactor": 1, "legendFormat": "Tx {{device}} ", "range": true, "refId": "B", "step": 240}], "title": "Network Traffic Basic", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Percentage of filesystem space used for each mounted device", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 13}, "id": 152, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "((node_filesystem_size_bytes{instance=\"$node\", job=\"$job\", device!~\"rootfs\"} - node_filesystem_avail_bytes{instance=\"$node\", job=\"$job\", device!~\"rootfs\"}) / node_filesystem_size_bytes{instance=\"$node\", job=\"$job\", device!~\"rootfs\"}) * 100", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "range": true, "refId": "A", "step": 240}], "title": "Disk Space Used Basic", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 265, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "CPU time usage split by state, normalized across all CPU cores", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 70, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "percent"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Idle - Waiting for something to happen"}, "properties": [{"id": "color", "value": {"fixedColor": "#052B51", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Iowait - Waiting for <PERSON><PERSON><PERSON> to complete"}, "properties": [{"id": "color", "value": {"fixedColor": "#EAB839", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Irq - Servicing interrupts"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Nice - Niced processes executing in user mode"}, "properties": [{"id": "color", "value": {"fixedColor": "#C15C17", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Softirq - Servicing softirqs"}, "properties": [{"id": "color", "value": {"fixedColor": "#E24D42", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Steal - Time spent in other operating systems when running in a virtualized environment"}, "properties": [{"id": "color", "value": {"fixedColor": "#FCE2DE", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "System - Processes executing in kernel mode"}, "properties": [{"id": "color", "value": {"fixedColor": "#508642", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "User - Normal processes executing in user mode"}, "properties": [{"id": "color", "value": {"fixedColor": "#5195CE", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Guest CPU usage"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "custom.stacking", "value": {"group": "A", "mode": "none"}}]}]}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 21}, "id": 3, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 250}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"system\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "System - Processes executing in kernel mode", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"user\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "User - Normal processes executing in user mode", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"nice\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Nice - Niced processes executing in user mode", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"iowait\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Iowait - Waiting for <PERSON><PERSON><PERSON> to complete", "range": true, "refId": "D", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"irq\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Irq - Servicing interrupts", "range": true, "refId": "E", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"softirq\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Softirq - Servicing softirqs", "range": true, "refId": "F", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"steal\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Steal - Time spent in other operating systems when running in a virtualized environment", "range": true, "refId": "G", "step": 240}, {"editorMode": "code", "expr": "sum(irate(node_cpu_seconds_total{mode=\"idle\",instance=\"$node\",job=\"$job\"}[$__rate_interval])) / scalar(count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu)))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Idle - Waiting for something to happen", "range": true, "refId": "H", "step": 240}, {"editorMode": "code", "expr": "sum by(instance) (irate(node_cpu_guest_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])) / on(instance) group_left sum by (instance)((irate(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval]))) > 0", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Guest CPU usage", "range": true, "refId": "I", "step": 240}], "title": "CPU", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Breakdown of physical memory and swap usage. Hardware-detected memory errors are also displayed", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Apps"}, "properties": [{"id": "color", "value": {"fixedColor": "#629E51", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Buffers"}, "properties": [{"id": "color", "value": {"fixedColor": "#614D93", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#6D1F62", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#511749", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Committed"}, "properties": [{"id": "color", "value": {"fixedColor": "#508642", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Free"}, "properties": [{"id": "color", "value": {"fixedColor": "#0A437C", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Hardware Corrupted - Amount of RAM that the kernel identified as corrupted / not working"}, "properties": [{"id": "color", "value": {"fixedColor": "#CFFAFF", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Inactive"}, "properties": [{"id": "color", "value": {"fixedColor": "#584477", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "PageTables"}, "properties": [{"id": "color", "value": {"fixedColor": "#0A50A1", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Page_Tables"}, "properties": [{"id": "color", "value": {"fixedColor": "#0A50A1", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "RAM_Free"}, "properties": [{"id": "color", "value": {"fixedColor": "#E0F9D7", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Slab"}, "properties": [{"id": "color", "value": {"fixedColor": "#806EB7", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Slab_Cache"}, "properties": [{"id": "color", "value": {"fixedColor": "#E0752D", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Swap - Swap memory usage"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Swap_<PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#C15C17", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Swap_Free"}, "properties": [{"id": "color", "value": {"fixedColor": "#2F575E", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Unused"}, "properties": [{"id": "color", "value": {"fixedColor": "#EAB839", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Unused - Free memory unassigned"}, "properties": [{"id": "color", "value": {"fixedColor": "#052B51", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*Hardware Corrupted - *./"}, "properties": [{"id": "custom.stacking", "value": {"group": false, "mode": "normal"}}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 21}, "id": 24, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_MemTotal_bytes{instance=\"$node\",job=\"$job\"} - node_memory_MemFree_bytes{instance=\"$node\",job=\"$job\"} - node_memory_Buffers_bytes{instance=\"$node\",job=\"$job\"} - node_memory_Cached_bytes{instance=\"$node\",job=\"$job\"} - node_memory_Slab_bytes{instance=\"$node\",job=\"$job\"} - node_memory_PageTables_bytes{instance=\"$node\",job=\"$job\"} - node_memory_SwapCached_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Apps - Memory used by user-space applications", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_PageTables_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "PageTables - Memory used to map between virtual and physical memory addresses", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_SwapCached_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "SwapCache - Memory that keeps track of pages that have been fetched from swap but not yet been modified", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_memory_Slab_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Slab - Memory used by the kernel to cache data structures for its own use (caches like inode, dentry, etc)", "range": true, "refId": "D", "step": 240}, {"editorMode": "code", "expr": "node_memory_Cached_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Cache - Parked file data (file content) cache", "range": true, "refId": "E", "step": 240}, {"editorMode": "code", "expr": "node_memory_Buffers_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Buffers - Block device (e.g. harddisk) cache", "range": true, "refId": "F", "step": 240}, {"editorMode": "code", "expr": "node_memory_MemFree_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Unused - Free memory unassigned", "range": true, "refId": "G", "step": 240}, {"editorMode": "code", "expr": "(node_memory_SwapTotal_bytes{instance=\"$node\",job=\"$job\"} - node_memory_SwapFree_bytes{instance=\"$node\",job=\"$job\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Swap - Swap space used", "range": true, "refId": "H", "step": 240}, {"editorMode": "code", "expr": "node_memory_HardwareCorrupted_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Hardware Corrupted - Amount of RAM that the kernel identified as corrupted / not working", "range": true, "refId": "I", "step": 240}], "title": "Memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Incoming and outgoing network traffic per interface", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 303}, "id": 84, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])*8", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "rate(node_network_transmit_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])*8", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "B", "step": 240}], "title": "Network Traffic", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Network interface utilization as a percentage of its maximum capacity", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 303}, "id": 338, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "(rate(node_network_receive_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])\n / ignoring(speed) node_network_speed_bytes{instance=\"$node\",job=\"$job\", speed!=\"-1\"}) * 100", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "(rate(node_network_transmit_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])\n / ignoring(speed) node_network_speed_bytes{instance=\"$node\",job=\"$job\", speed!=\"-1\"}) * 100", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "B", "step": 240}], "title": "Network Saturation", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Disk I/O operations per second for each device", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "read (-) / write (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "iops"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Read.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 315}, "id": 229, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_reads_completed_total{instance=\"$node\",job=\"$job\",device=~\"$diskdevices\"}[$__rate_interval])", "intervalFactor": 4, "legendFormat": "{{device}} - Read", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_writes_completed_total{instance=\"$node\",job=\"$job\",device=~\"$diskdevices\"}[$__rate_interval])", "intervalFactor": 1, "legendFormat": "{{device}} - Write", "range": true, "refId": "B", "step": 240}], "title": "Disk IOps", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Disk I/O throughput per device", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "read (-) / write (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Read*./"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 315}, "id": 42, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_read_bytes_total{instance=\"$node\",job=\"$job\",device=~\"$diskdevices\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{device}} - Read", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_written_bytes_total{instance=\"$node\",job=\"$job\",device=~\"$diskdevices\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{device}} - Write", "range": true, "refId": "B", "step": 240}], "title": "Disk Throughput", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Amount of available disk space per mounted filesystem, excluding rootfs. Based on block availability to non-root users", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 327}, "id": 43, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_filesystem_avail_bytes{instance=\"$node\",job=\"$job\",device!~'rootfs'}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "metric": "", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_filesystem_free_bytes{instance=\"$node\",job=\"$job\",device!~'rootfs'}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{mountpoint}} - Free", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_filesystem_size_bytes{instance=\"$node\",job=\"$job\",device!~'rootfs'}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{mountpoint}} - Size", "range": true, "refId": "C", "step": 240}], "title": "Filesystem Space Available", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Disk usage (used = total - available) per mountpoint", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 327}, "id": 156, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_filesystem_size_bytes{instance=\"$node\",job=\"$job\",device!~'rootfs'} - node_filesystem_avail_bytes{instance=\"$node\",job=\"$job\",device!~'rootfs'}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "range": true, "refId": "A", "step": 240}], "title": "Filesystem Used", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Percentage of time the disk was actively processing I/O operations", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 40, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 339}, "id": 127, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_io_time_seconds_total{instance=\"$node\",job=\"$job\",device=~\"$diskdevices\"} [$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{device}}", "range": true, "refId": "A", "step": 240}], "title": "Disk I/O Utilization", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "How often tasks experience CPU, memory, or I/O delays. “Some” indicates partial slowdown; “Full” indicates all tasks are stalled. Based on Linux PSI metrics:\nhttps://docs.kernel.org/accounting/psi.html", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "some (-) / full (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Some.*/"}, "properties": [{"id": "custom.fillOpacity", "value": 0}]}, {"matcher": {"id": "byRegexp", "options": "/.*Some.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 339}, "id": 322, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_pressure_cpu_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "CPU - Some", "range": true, "refId": "CPU some", "step": 240}, {"editorMode": "code", "expr": "rate(node_pressure_memory_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Memory - Some", "range": true, "refId": "Memory some", "step": 240}, {"editorMode": "code", "expr": "rate(node_pressure_memory_stalled_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Memory - Full", "range": true, "refId": "Memory full", "step": 240}, {"editorMode": "code", "expr": "rate(node_pressure_io_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "I/O - Some", "range": true, "refId": "I/O some", "step": 240}, {"editorMode": "code", "expr": "rate(node_pressure_io_stalled_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "I/O - Full", "range": true, "refId": "I/O full", "step": 240}, {"editorMode": "code", "expr": "rate(node_pressure_irq_stalled_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "IRQ - Full", "range": true, "refId": "A", "step": 240}], "title": "Pressure Stall Information", "type": "timeseries"}], "title": "CPU / Memory / Net / Disk", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 266, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Displays committed memory usage versus the system's commit limit. Exceeding the limit is allowed under Linux overcommit policies but may increase OOM risks under high load", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*CommitLimit - *./"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 602}, "id": 135, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_Committed_AS_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Committed_AS – Memory promised to processes (not necessarily used)", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_CommitLimit_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "CommitLimit - Max allowable committed memory", "range": true, "refId": "B", "step": 240}], "title": "Memory Committed", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Memory currently dirty (modified but not yet written to disk), being actively written back, or held by writeback buffers. High dirty or writeback memory may indicate disk I/O pressure or delayed flushing", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 602}, "id": 130, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_Writeback_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Writeback – Memory currently being flushed to disk", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_WritebackTmp_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "WritebackTmp – FUSE temporary writeback buffers", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_Dirty_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Dirty – Memory marked dirty (pending write to disk)", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_memory_NFS_Unstable_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "NFS Unstable – Pages sent to NFS server, awaiting storage commit", "range": true, "refId": "D", "step": 240}], "title": "Memory Writeback and Dirty", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Kernel slab memory usage, separated into reclaimable and non-reclaimable categories. Reclaimable memory can be freed under memory pressure (e.g., caches), while unreclaimable memory is locked by the kernel for core functions", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 802}, "id": 131, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_SUnreclaim_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "SUnreclaim – Non-reclaimable slab memory (kernel objects)", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_SReclaimable_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "SReclaimable – Potentially reclaimable slab memory (e.g., inode cache)", "range": true, "refId": "B", "step": 240}], "title": "Memory Slab", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Memory used for mapped files (such as libraries) and shared memory (shmem and tmpfs), including variants backed by huge pages", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 802}, "id": 138, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_Mapped_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Mapped – Memory mapped from files (e.g., libraries, mmap)", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_Shmem_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "<PERSON>hmem – Shared memory used by processes and tmpfs", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_ShmemHugePages_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "ShmemHugePages – Shared memory (shmem/tmpfs) allocated with HugePages", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_memory_ShmemPmdMapped_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PMD Mapped – Shmem/tmpfs backed by Transparent HugePages (PMD)", "range": true, "refId": "D", "step": 240}], "title": "Memory Shared and Mapped", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Proportion of memory pages in the kernel's active and inactive LRU lists relative to total RAM. Active pages have been recently used, while inactive pages are less recently accessed but still resident in memory", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Active.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*Inactive.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 812}, "id": 136, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "(node_memory_Inactive_bytes{instance=\"$node\",job=\"$job\"}) \n/ \n(node_memory_MemTotal_bytes{instance=\"$node\",job=\"$job\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "Inactive – Less recently used memory, more likely to be reclaimed", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "(node_memory_Active_bytes{instance=\"$node\",job=\"$job\"}) \n/ \n(node_memory_MemTotal_bytes{instance=\"$node\",job=\"$job\"})\n", "format": "time_series", "intervalFactor": 1, "legendFormat": "Active – Recently used memory, retained unless under pressure", "range": true, "refId": "B", "step": 240}], "title": "Memory LRU Active / Inactive (%)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Breakdown of memory pages in the kernel's active and inactive LRU lists, separated by anonymous (heap, tmpfs) and file-backed (caches, mmap) pages.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 812}, "id": 191, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_Inactive_file_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Inactive_file - File-backed memory on inactive LRU list", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_Inactive_anon_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Inactive_anon – Anonymous memory on inactive LRU (incl. tmpfs & swap cache)", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_Active_file_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Active_file - File-backed memory on active LRU list", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_memory_Active_anon_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Active_anon – Anonymous memory on active LRU (incl. tmpfs & swap cache)", "range": true, "refId": "D", "step": 240}], "title": "Memory LRU Active / Inactive Detail", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks kernel memory used for CPU-local structures, per-thread stacks, and bounce buffers used for I/O on DMA-limited devices. These areas are typically small but critical for low-level operations", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 822}, "id": 160, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_KernelStack_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "KernelStack – Kernel stack memory (per-thread, non-reclaimable)", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_Percpu_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PerCPU – Dynamically allocated per-CPU memory (used by kernel modules)", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_Bounce_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Bounce Memory – I/O buffer for DMA-limited devices", "range": true, "refId": "C", "step": 240}], "title": "Memory Kernel / CPU / IO", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Usage of the kernel's vmalloc area, which provides virtual memory allocations for kernel modules and drivers. Includes total, used, and largest free block sizes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Total.*/"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 822}, "id": 70, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_VmallocChunk_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Vmalloc Free Chunk – Largest available block in vmalloc area", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_VmallocTotal_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Vmalloc Total – Total size of the vmalloc memory area", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_VmallocUsed_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Vmalloc Used – Portion of vmalloc area currently in use", "range": true, "refId": "C", "step": 240}], "title": "Memory Vmalloc", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Memory used by anonymous pages (not backed by files), including standard and huge page allocations. Includes heap, stack, and memory-mapped anonymous regions", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 832}, "id": 129, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_AnonHugePages_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "AnonHugePages – Anonymous memory using HugePages", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_AnonPages_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "AnonPages – Anonymous memory (non-file-backed)", "range": true, "refId": "B", "step": 240}], "title": "Memory Anonymous", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Memory that is locked in RAM and cannot be swapped out. Includes both kernel-unevictable memory and user-level memory locked with mlock()", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Hardware Corrupted - Amount of RAM that the kernel identified as corrupted / not working"}, "properties": [{"id": "color", "value": {"fixedColor": "#CFFAFF", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 832}, "id": 137, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_Unevictable_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Unevictable – Kernel-pinned memory (not swappable)", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_Mlocked_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Mlocked – Application-locked memory via mlock()", "range": true, "refId": "B", "step": 240}], "title": "Memory Unevictable and MLocked", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "How much memory is directly mapped in the kernel using different page sizes (4K, 2M, 1G). Helps monitor large page utilization in the direct map region", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Active"}, "properties": [{"id": "color", "value": {"fixedColor": "#99440A", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Buffers"}, "properties": [{"id": "color", "value": {"fixedColor": "#58140C", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#6D1F62", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#511749", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Committed"}, "properties": [{"id": "color", "value": {"fixedColor": "#508642", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Dirty"}, "properties": [{"id": "color", "value": {"fixedColor": "#6ED0E0", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Free"}, "properties": [{"id": "color", "value": {"fixedColor": "#B7DBAB", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Inactive"}, "properties": [{"id": "color", "value": {"fixedColor": "#EA6460", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Mapped"}, "properties": [{"id": "color", "value": {"fixedColor": "#052B51", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "PageTables"}, "properties": [{"id": "color", "value": {"fixedColor": "#0A50A1", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Page_Tables"}, "properties": [{"id": "color", "value": {"fixedColor": "#0A50A1", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Slab_Cache"}, "properties": [{"id": "color", "value": {"fixedColor": "#EAB839", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#BF1B00", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Swap_<PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "#C15C17", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total"}, "properties": [{"id": "color", "value": {"fixedColor": "#511749", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total RAM"}, "properties": [{"id": "color", "value": {"fixedColor": "#052B51", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total RAM + Swap"}, "properties": [{"id": "color", "value": {"fixedColor": "#052B51", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "VmallocUsed"}, "properties": [{"id": "color", "value": {"fixedColor": "#EA6460", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 842}, "id": 128, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_DirectMap1G_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "DirectMap 1G – Memory mapped with 1GB pages", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_DirectMap2M_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "DirectMap 2M – Memory mapped with 2MB pages", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_DirectMap4k_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "DirectMap 4K – Memory mapped with 4KB pages", "range": true, "refId": "C", "step": 240}], "title": "Memory DirectMap", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Displays HugePages memory usage in bytes, including allocated, free, reserved, and surplus memory. All values are calculated based on the number of huge pages multiplied by their configured size", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 842}, "id": 140, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_memory_HugePages_Free{instance=\"$node\",job=\"$job\"} * node_memory_Hugepagesize_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "HugePages Used – Currently allocated", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_memory_HugePages_Rsvd{instance=\"$node\",job=\"$job\"} * node_memory_Hugepagesize_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "HugePages Reserved – Promised but unused", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_memory_HugePages_Surp{instance=\"$node\",job=\"$job\"} * node_memory_Hugepagesize_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "HugePages Surplus – Dynamic pool extension", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_memory_HugePages_Total{instance=\"$node\",job=\"$job\"} * node_memory_Hugepagesize_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HugePages Total – Reserved memory", "range": true, "refId": "D", "step": 240}], "title": "Memory HugePages", "type": "timeseries"}], "title": "Memory Meminfo", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 267, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of memory pages being read from or written to disk (page-in and page-out operations). High page-out may indicate memory pressure or swapping activity", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 603}, "id": 176, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_vmstat_pgpgin{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Pagesin - Page in ops", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_vmstat_pgpgout{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Pagesout - Page out ops", "range": true, "refId": "B", "step": 240}], "title": "Memory Pages In / Out", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate at which memory pages are being swapped in from or out to disk. High swap-out activity may indicate memory pressure", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 603}, "id": 22, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_vmstat_pswpin{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Pswpin - Pages swapped in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_vmstat_pswpout{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Pswpout - Pages swapped out", "range": true, "refId": "B", "step": 240}], "title": "Memory Pages Swap In / Out", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of memory page faults, split into total, major (disk-backed), and derived minor (non-disk) faults. High major fault rates may indicate memory pressure or insufficient RAM", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Pgfault - Page major and minor fault ops"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.stacking", "value": {"group": false, "mode": "none"}}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 783}, "id": 175, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 350}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_vmstat_pgfault{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Pgfault - Page major and minor fault ops", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_vmstat_pgmajfault{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Pgmajfault - Major page fault ops", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_vmstat_pgfault{instance=\"$node\",job=\"$job\"}[$__rate_interval])  - irate(node_vmstat_pgmajfault{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Pgminfault - Minor page fault ops", "range": true, "refId": "C", "step": 240}], "title": "Memory Page Faults", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of Out-of-Memory (OOM) kill events. A non-zero value indicates the kernel has terminated one or more processes due to memory exhaustion", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "OOM Kills"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 783}, "id": 307, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_vmstat_oom_kill{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "OOM Kills", "range": true, "refId": "A", "step": 240}], "title": "OOM Killer", "type": "timeseries"}], "title": "Memory Vmstat", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 293, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks the system clock's estimated and maximum error, as well as its offset from the reference clock (e.g., via NTP). Useful for detecting synchronization drift", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 604}, "id": 260, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_timex_estimated_error_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Estimated error", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_timex_offset_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Offset local vs reference", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_timex_maxerror_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Maximum error", "range": true, "refId": "C", "step": 240}], "title": "Time Synchronized Drift", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "NTP phase-locked loop (PLL) time constant used by the kernel to control time adjustments. Lower values mean faster correction but less stability", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 604}, "id": 291, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_timex_loop_time_constant{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PLL Time Constant", "range": true, "refId": "A", "step": 240}], "title": "Time PLL Adjust", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Shows whether the system clock is synchronized to a reliable time source, and the current frequency correction ratio applied by the kernel to maintain synchronization", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 754}, "id": 168, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_timex_sync_status{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Sync status (1 = ok)", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_timex_frequency_adjustment_ratio{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Frequency Adjustment", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_timex_tick_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "Tick <PERSON>", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_timex_tai_offset_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "TAI Offset", "range": true, "refId": "D", "step": 240}], "title": "Time Synchronized Status", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Displays the PPS signal's frequency offset and stability (jitter) in hertz. Useful for monitoring high-precision time sources like GPS or atomic clocks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "<PERSON>hz"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 754}, "id": 333, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_timex_pps_frequency_hertz{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PPS Frequency Offset", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_timex_pps_stability_hertz{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PPS Frequency Stability", "range": true, "refId": "B", "step": 240}], "title": "PPS Frequency / Stability", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks PPS signal timing jitter and shift compared to system clock", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 764}, "id": 334, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_timex_pps_jitter_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PPS Jitter", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_timex_pps_shift_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PPS Shift", "range": true, "refId": "B", "step": 240}], "title": "PPS Time Accuracy", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of PPS synchronization diagnostics including calibration events, jitter violations, errors, and frequency stability exceedances", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 764}, "id": 335, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_timex_pps_calibration_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PPS Calibrations/sec", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_timex_pps_error_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PPS Errors/sec", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_timex_pps_stability_exceeded_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "PPS Stability Exceeded/sec", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "irate(node_timex_pps_jitter_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "PPS Jitter Events/sec", "range": true, "refId": "D", "step": 240}], "title": "PPS Sync Events", "type": "timeseries"}], "title": "System Timesync", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 312, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Processes currently in runnable or blocked states. Helps identify CPU contention or I/O wait bottlenecks.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 605}, "id": 62, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_procs_blocked{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Blocked (I/O Wait)", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_procs_running{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Runnable (Ready for CPU)", "range": true, "refId": "B", "step": 240}], "title": "Processes Status", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Current number of processes in each state (e.g., running, sleeping, zombie). Requires --collector.processes to be enabled in node_exporter", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "D"}, "properties": [{"id": "displayName", "value": "Uninterruptible Sleeping"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "I"}, "properties": [{"id": "displayName", "value": "Idle Kernel Thread"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "R"}, "properties": [{"id": "displayName", "value": "Running"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "S"}, "properties": [{"id": "displayName", "value": "Interruptible Sleeping"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "T"}, "properties": [{"id": "displayName", "value": "Stopped"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "X"}, "properties": [{"id": "displayName", "value": "Dead"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Z"}, "properties": [{"id": "displayName", "value": "Zombie"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 605}, "id": 315, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_processes_state{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ state }}", "range": true, "refId": "A", "step": 240}], "title": "Processes Detailed States", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of new processes being created on the system (forks/sec).", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 635}, "id": 148, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_forks_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Process Forks per second", "range": true, "refId": "A", "step": 240}], "title": "Processes Forks", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Shows CPU saturation per core, calculated as the proportion of time spent waiting to run relative to total time demanded (running + waiting).", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*waiting.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 635}, "id": 305, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_schedstat_running_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{ cpu }} - Running", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_schedstat_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{cpu}} - Waiting Queue", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_schedstat_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])\n/\n(irate(node_schedstat_running_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval]) + irate(node_schedstat_waiting_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval]))\n", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{cpu}}", "range": true, "refId": "C", "step": 240}], "title": "CPU Saturation per Core", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of active PIDs on the system and the configured maximum allowed. Useful for detecting PID exhaustion risk. Requires --collector.processes in node_exporter", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "PIDs limit"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 645}, "id": 313, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_processes_pids{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Number of PIDs", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_processes_max_processes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "PIDs limit", "range": true, "refId": "B", "step": 240}], "title": "PIDs Number and Limit", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of active threads on the system and the configured thread limit. Useful for monitoring thread pressure. Requires --collector.processes in node_exporter", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Threads limit"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 645}, "id": 314, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_processes_threads{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Allocated threads", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_processes_max_threads{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Threads limit", "range": true, "refId": "B", "step": 240}], "title": "Threads Number and Limit", "type": "timeseries"}], "title": "System Processes", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 269, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Per-second rate of context switches and hardware interrupts. High values may indicate intense CPU or I/O activity", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 686}, "id": 8, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_context_switches_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Context switches", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_intr_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Interrupts", "range": true, "refId": "B", "step": 240}], "title": "Context Switches / Interrupts", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "System load average over 1, 5, and 15 minutes. Reflects the number of active or waiting processes. Values above CPU core count may indicate overload", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Core Count"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 686}, "id": 7, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_load1{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Load 1m", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_load5{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Load 5m", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_load15{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Load 15m", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "count(count(node_cpu_seconds_total{instance=\"$node\",job=\"$job\"}) by (cpu))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "CPU Core Count", "range": true, "refId": "D", "step": 240}], "title": "System Load", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Real-time CPU frequency scaling per core, including average minimum and maximum allowed scaling frequencies", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "hertz"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Max"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}, {"id": "custom.hideFrom", "value": {"legend": true, "tooltip": false, "viz": false}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Min"}, "properties": [{"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}, {"id": "custom.hideFrom", "value": {"legend": true, "tooltip": false, "viz": false}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 696}, "id": 321, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_cpu_scaling_frequency_hertz{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{ cpu }}", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "avg(node_cpu_scaling_frequency_max_hertz{instance=\"$node\",job=\"$job\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Max", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "avg(node_cpu_scaling_frequency_min_hertz{instance=\"$node\",job=\"$job\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Min", "range": true, "refId": "C", "step": 240}], "title": "CPU Frequency Scaling", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of scheduling timeslices executed per CPU. Reflects how frequently the scheduler switches tasks on each core", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 696}, "id": 306, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_schedstat_timeslices_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{ cpu }}", "range": true, "refId": "A", "step": 240}], "title": "CPU Schedule Timeslices", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Breaks down hardware interrupts by type and device. Useful for diagnosing IRQ load on network, disk, or CPU interfaces. Requires --collector.interrupts to be enabled in node_exporter", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 706}, "id": 259, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_interrupts_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ type }} - {{ info }}", "range": true, "refId": "A", "step": 240}], "title": "IRQ Detail", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of bits of entropy currently available to the system's random number generators (e.g., /dev/random). Low values may indicate that random number generation could block or degrade performance of cryptographic operations", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "decbits"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Entropy pool max"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 706}, "id": 151, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_entropy_available_bits{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Entropy available", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_entropy_pool_size_bits{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Entropy pool max", "range": true, "refId": "B", "step": 240}], "title": "Entropy", "type": "timeseries"}], "title": "System Misc", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 304, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Monitors hardware sensor temperatures and critical thresholds as exposed by Linux hwmon. Includes CPU, GPU, and motherboard sensors where available", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "celsius"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Critical*./"}, "properties": [{"id": "color", "value": {"fixedColor": "#E24D42", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 607}, "id": 158, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_hwmon_temp_celsius{instance=\"$node\",job=\"$job\"} * on(chip) group_left(chip_name) node_hwmon_chip_names{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ chip_name }} {{ sensor }}", "range": true, "refId": "A", "step": 240}, {"expr": "node_hwmon_temp_crit_alarm_celsius{instance=\"$node\",job=\"$job\"} * on(chip) group_left(chip_name) node_hwmon_chip_names{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{ chip_name }} {{ sensor }} Critical Alarm", "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_hwmon_temp_crit_celsius{instance=\"$node\",job=\"$job\"} * on(chip) group_left(chip_name) node_hwmon_chip_names{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ chip_name }} {{ sensor }} Critical", "range": true, "refId": "C", "step": 240}, {"expr": "node_hwmon_temp_crit_hyst_celsius{instance=\"$node\",job=\"$job\"} * on(chip) group_left(chip_name) node_hwmon_chip_names{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{ chip_name }} {{ sensor }} Critical Historical", "refId": "D", "step": 240}, {"expr": "node_hwmon_temp_max_celsius{instance=\"$node\",job=\"$job\"} * on(chip) group_left(chip_name) node_hwmon_chip_names{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{ chip_name }} {{ sensor }} Max", "refId": "E", "step": 240}], "title": "Hardware Temperature Monitor", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Shows how hard each cooling device (fan/throttle) is working relative to its maximum capacity", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*<PERSON>*./"}, "properties": [{"id": "color", "value": {"fixedColor": "#EF843C", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 607}, "id": 300, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "100 * node_cooling_device_cur_state{instance=\"$node\",job=\"$job\"} / node_cooling_device_max_state{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{ name }} - {{ type }} ", "range": true, "refId": "A", "step": 240}], "title": "Cooling Device Utilization", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Shows the online status of power supplies (e.g., AC, battery). A value of 1-Yes indicates the power supply is active/online", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bool_yes_no"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 617}, "id": 302, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_power_supply_online{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{ power_supply }} online", "range": true, "refId": "A", "step": 240}], "title": "Power Supply", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Displays the current fan speeds (RPM) from hardware sensors via the hwmon interface", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "rotrpm"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 617}, "id": 325, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_hwmon_fan_rpm{instance=\"$node\",job=\"$job\"} * on(chip) group_left(chip_name) node_hwmon_chip_names{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ chip_name }} {{ sensor }}", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_hwmon_fan_min_rpm{instance=\"$node\",job=\"$job\"} * on(chip) group_left(chip_name) node_hwmon_chip_names{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{ chip_name }} {{ sensor }} rpm min", "range": true, "refId": "B", "step": 240}], "title": "Hardware Fan Speed", "type": "timeseries"}], "title": "Hardware Misc", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 296, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Current number of systemd units in each operational state, such as active, failed, inactive, or transitioning", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Failed"}, "properties": [{"id": "color", "value": {"fixedColor": "#F2495C", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Active"}, "properties": [{"id": "color", "value": {"fixedColor": "#73BF69", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Activating"}, "properties": [{"id": "color", "value": {"fixedColor": "#C8F2C2", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Deactivating"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Inactive"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 4098}, "id": 298, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_systemd_units{instance=\"$node\",job=\"$job\",state=\"activating\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Activating", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_systemd_units{instance=\"$node\",job=\"$job\",state=\"active\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Active", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_systemd_units{instance=\"$node\",job=\"$job\",state=\"deactivating\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Deactivating", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_systemd_units{instance=\"$node\",job=\"$job\",state=\"failed\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Failed", "range": true, "refId": "D", "step": 240}, {"editorMode": "code", "expr": "node_systemd_units{instance=\"$node\",job=\"$job\",state=\"inactive\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Inactive", "range": true, "refId": "E", "step": 240}], "title": "Systemd Units State", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Current number of active connections per systemd socket, as reported by the Node Exporter systemd collector", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 4098}, "id": 331, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_systemd_socket_current_connections{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ name }}", "range": true, "refId": "A", "step": 240}], "title": "Systemd Sockets Current", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of accepted connections per second for each systemd socket", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "eps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 4108}, "id": 297, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_systemd_socket_accepted_connections_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ name }}", "range": true, "refId": "A", "step": 240}], "title": "Systemd Sockets Accepted", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of systemd socket connection refusals per second, typically due to service unavailability or backlog overflow", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "eps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 4108}, "id": 332, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_systemd_socket_refused_connections_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ name }}", "range": true, "refId": "A", "step": 240}], "title": "Systemd Sockets Refused", "type": "timeseries"}], "title": "Systemd", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 270, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of I/O operations completed per second for the device (after merges), including both reads and writes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "read (–) / write (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "iops"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Read.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 29}, "id": 9, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_reads_completed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "intervalFactor": 1, "legendFormat": "{{device}} - Read", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_writes_completed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "intervalFactor": 1, "legendFormat": "{{device}} - Write", "range": true, "refId": "B", "step": 240}], "title": "Disk Read/Write IOps", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of bytes read from or written to the device per second", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "read (–) / write (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Read.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 29}, "id": 33, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_read_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Read", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "exemplar": false, "expr": "irate(node_disk_written_bytes_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{device}} - Write", "range": true, "refId": "B", "step": 240}], "title": "Disk Read/Write Data", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Average time for requests issued to the device to be served. This includes the time spent by the requests in queue and the time spent servicing them.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "read (–) / write (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Read.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 259}, "id": 37, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_read_time_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval]) / irate(node_disk_reads_completed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Read", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_write_time_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval]) / irate(node_disk_writes_completed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Write", "range": true, "refId": "B", "step": 240}], "title": "Disk Average Wait Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Average queue length of the requests that were issued to the device", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/sda_*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#7EB26D", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 259}, "id": 35, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_io_time_weighted_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}}", "range": true, "refId": "A", "step": 240}], "title": "Average Queue Size", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of read and write requests merged per second that were queued to the device", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "read (–) / write (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "iops"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Read.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 269}, "id": 133, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_reads_merged_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "intervalFactor": 1, "legendFormat": "{{device}} - Read", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_writes_merged_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "intervalFactor": 1, "legendFormat": "{{device}} - Write", "range": true, "refId": "B", "step": 240}], "title": "Disk R/W <PERSON>rged", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Percentage of time the disk spent actively processing I/O operations, including general I/O, discards (TRIM), and write cache flushes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 269}, "id": 36, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_io_time_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - General IO", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_discard_time_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Discard/TRIM", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_flush_requests_time_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Flush (write cache)", "range": true, "refId": "C", "step": 240}], "title": "Time Spent Doing I/Os", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Per-second rate of discard (TRIM) and flush (write cache) operations. Useful for monitoring low-level disk activity on SSDs and advanced storage", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 279}, "id": 301, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_discards_completed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Discards completed", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_discards_merged_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Discards merged", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_disk_flush_requests_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Flush", "range": true, "refId": "C", "step": 240}], "title": "Disk Ops Discards / Flush", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Shows how many disk sectors are discarded (TRIMed) per second. Useful for monitoring SSD behavior and storage efficiency", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 279}, "id": 326, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_disk_discarded_sectors_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}}", "range": true, "refId": "A", "step": 240}], "title": "Disk Sectors Discarded Successfully", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of in-progress I/O requests at the time of sampling (active requests in the disk queue)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/sda.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 289}, "id": 34, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_disk_io_now{instance=\"$node\",job=\"$job\"}", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}}", "range": true, "refId": "A", "step": 240}], "title": "Instantaneous Queue Size", "type": "timeseries"}], "title": "Storage Disk", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 271, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of file descriptors currently allocated system-wide versus the system limit. Important for detecting descriptor exhaustion risks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "sishort"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*<PERSON>.*/"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 30}, "id": 28, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_filefd_maximum{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max open files", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_filefd_allocated{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Open files", "range": true, "refId": "B", "step": 240}], "title": "File Descriptor", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of free file nodes (inodes) available per mounted filesystem. A low count may prevent file creation even if disk space is available", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 30}, "id": 41, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_filesystem_files_free{instance=\"$node\",job=\"$job\",device!~'rootfs'}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "range": true, "refId": "A", "step": 240}], "title": "File Nodes Free", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Indicates filesystems mounted in read-only mode or reporting device-level I/O errors.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bool_yes_no"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 240}, "id": 44, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_filesystem_readonly{instance=\"$node\",job=\"$job\",device!~'rootfs'}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{mountpoint}} - ReadOnly", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_filesystem_device_error{instance=\"$node\",job=\"$job\",device!~'rootfs',fstype!~'tmpfs'}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{mountpoint}} - Device error", "range": true, "refId": "B", "step": 240}], "title": "Filesystem in ReadOnly / Error", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of file nodes (inodes) available per mounted filesystem. Reflects maximum file capacity regardless of disk size", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "sishort"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 240}, "id": 219, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_filesystem_files{instance=\"$node\",job=\"$job\",device!~'rootfs'}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "range": true, "refId": "A", "step": 240}], "title": "File Nodes Size", "type": "timeseries"}], "title": "Storage Filesystem", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 30}, "id": 272, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of network packets received and transmitted per second, by interface.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 31}, "id": 60, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_packets_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "rate(node_network_transmit_packets_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "B", "step": 240}], "title": "Network Traffic by Packets", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of packet-level errors for each network interface. Receive errors may indicate physical or driver issues; transmit errors may reflect collisions or hardware faults", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 31}, "id": 142, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_errs_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "rate(node_network_transmit_errs_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "B", "step": 240}], "title": "Network Traffic Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of dropped packets per network interface. Receive drops can indicate buffer overflow or driver issues; transmit drops may result from outbound congestion or queuing limits", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 121}, "id": 143, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_drop_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "rate(node_network_transmit_drop_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "B", "step": 240}], "title": "Network Traffic Drop", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of compressed network packets received and transmitted per interface. These are common in low-bandwidth or special interfaces like PPP or SLIP", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 121}, "id": 141, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_compressed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "rate(node_network_transmit_compressed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "B", "step": 240}], "title": "Network Traffic Compressed", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of incoming multicast packets received per network interface. Multicast is used by protocols such as mDNS, SSDP, and some streaming or cluster services", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 131}, "id": 146, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_multicast_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}], "title": "Network Traffic Multicast", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of received packets that could not be processed due to missing protocol or handler in the kernel. May indicate unsupported traffic or misconfiguration", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 131}, "id": 327, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_nohandler_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}], "title": "Network Traffic NoHandler", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of frame errors on received packets, typically caused by physical layer issues such as bad cables, duplex mismatches, or hardware problems", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 141}, "id": 145, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_frame_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}], "title": "Network Traffic Frame", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks FIFO buffer overrun errors on network interfaces. These occur when incoming or outgoing packets are dropped due to queue or buffer overflows, often indicating congestion or hardware limits", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 141}, "id": 144, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_receive_fifo_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "rate(node_network_transmit_fifo_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "B", "step": 240}], "title": "Network Traffic Fifo", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of packet collisions detected during transmission. Mostly relevant on half-duplex or legacy Ethernet networks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 151}, "id": 232, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_transmit_colls_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "A", "step": 240}], "title": "Network Traffic Collision", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of carrier errors during transmission. These typically indicate physical layer issues like faulty cabling or duplex mismatches", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 151}, "id": 231, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "rate(node_network_transmit_carrier_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{device}} - Tx out", "range": true, "refId": "A", "step": 240}], "title": "Network Traffic Carrier Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of ARP entries per interface. Useful for detecting excessive ARP traffic or table growth due to scanning or misconfiguration", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 161}, "id": 230, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_arp_entries{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{ device }} ARP Table", "range": true, "refId": "A", "step": 240}], "title": "ARP Entries", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Current and maximum connection tracking entries used by Netfilter (nf_conntrack). High usage approaching the limit may cause packet drops or connection issues", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "NF conntrack limit"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 161}, "id": 61, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_nf_conntrack_entries{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "NF conntrack entries", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_nf_conntrack_entries_limit{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "NF conntrack limit", "range": true, "refId": "B", "step": 240}], "title": "NF Conntrack", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Operational and physical link status of each network interface. Values are Yes for 'up' or link present, and No for 'down' or no carrier.\"", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bool_yes_no"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 171}, "id": 309, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_network_up{operstate=\"up\",instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{interface}} - Operational state UP", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_network_carrier{instance=\"$node\",job=\"$job\"}", "format": "time_series", "instant": false, "legendFormat": "{{device}} - Physical link", "refId": "B"}], "title": "Network Operational Status", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Maximum speed of each network interface as reported by the operating system. This is a static hardware capability, not current throughput", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 10, "w": 6, "x": 12, "y": 171}, "id": 280, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 30, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "valueMode": "color"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_network_speed_bytes{instance=\"$node\",job=\"$job\"} * 8", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{ device }}", "range": true, "refId": "A", "step": 240}], "title": "Speed", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "MTU (Maximum Transmission Unit) in bytes for each network interface. Affects packet size and transmission efficiency", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 10, "w": 6, "x": 18, "y": 171}, "id": 288, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 30, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "manual", "valueMode": "color"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_network_mtu_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{ device }}", "range": true, "refId": "A", "step": 240}], "title": "MTU", "type": "bargauge"}], "title": "Network Traffic", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 31}, "id": 273, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks TCP socket usage and memory per node", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 32}, "id": 63, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_sockstat_TCP_alloc{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Allocated Sockets", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_TCP_inuse{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "In-Use Sockets", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_TCP_orphan{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Orphaned Sockets", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_TCP_tw{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "TIME_WAIT Sockets", "range": true, "refId": "D", "step": 240}], "title": "Sockstat TCP", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of UDP and UDPLite sockets currently in use", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 32}, "id": 124, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_sockstat_UDPLITE_inuse{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDPLite - In-Use Sockets", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_UDP_inuse{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP - In-Use Sockets", "range": true, "refId": "B", "step": 240}], "title": "Sockstat UDP", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total number of sockets currently in use across all protocols (TCP, UDP, UNIX, etc.), as reported by /proc/net/sockstat", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 122}, "id": 126, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_sockstat_sockets_used{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Total sockets", "range": true, "refId": "A", "step": 240}], "title": "Sockstat Used", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of FRAG and RAW sockets currently in use. RAW sockets are used for custom protocols or tools like ping; FRAG sockets are used internally for IP packet defragmentation", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 122}, "id": 125, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_sockstat_FRAG_inuse{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "FRAG - In-Use Sockets", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_RAW_inuse{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "RAW - In-Use Sockets", "range": true, "refId": "C", "step": 240}], "title": "Sockstat FRAG / RAW", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "TCP/UDP socket memory usage in kernel (in pages)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 132}, "id": 336, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_sockstat_TCP_mem{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "TCP", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_UDP_mem{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP", "range": true, "refId": "B", "step": 240}], "title": "TCP/UDP Kernel Buffer Memory Pages", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Kernel memory used by TCP, UDP, and IP fragmentation buffers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 132}, "id": 220, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_sockstat_TCP_mem_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "TCP", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_UDP_mem_bytes{instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_sockstat_FRAG_memory{instance=\"$node\",job=\"$job\"}", "interval": "", "intervalFactor": 1, "legendFormat": "Fragmentation", "range": true, "refId": "C"}], "title": "Sockstat Memory Size", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Packets processed and dropped by the softnet network stack per CPU. Drops may indicate CPU saturation or network driver limitations", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "drop (-) / process (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Dropped.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 142}, "id": 290, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_softnet_processed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{cpu}} - Processed", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_softnet_dropped_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{cpu}} - Dropped", "range": true, "refId": "B", "step": 240}], "title": "Softnet Packets", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "How often the kernel was unable to process all packets in the softnet queue before time ran out. Frequent squeezes may indicate CPU contention or driver inefficiency", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "eps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 142}, "id": 310, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_softnet_times_squeezed_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{cpu}} - Times Squeezed", "range": true, "refId": "A", "step": 240}], "title": "Softnet Out of Quota", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks the number of packets processed or dropped by Receive Packet Steering (RPS), a mechanism to distribute packet processing across CPUs", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Dropped.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 152}, "id": 330, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_softnet_received_rps_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{cpu}} - Processed", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_softnet_flow_limit_count_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "CPU {{cpu}} - Dropped", "range": true, "refId": "B", "step": 240}], "title": "Softnet RPS", "type": "timeseries"}], "title": "Network Sockstat", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 32}, "id": 274, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of octets sent and received at the IP layer, as reported by /proc/net/netstat", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 33}, "id": 221, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true, "width": 300}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_IpExt_InOctets{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "IP Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_IpExt_OutOctets{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "intervalFactor": 1, "legendFormat": "IP Tx out", "range": true, "refId": "B", "step": 240}], "title": "Netstat IP In / Out Octets", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of TCP segments sent and received per second, including data and control segments", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}, {"matcher": {"id": "byRegexp", "options": "/.*Snd.*/"}, "properties": []}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 33}, "id": 299, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_Tcp_InSegs{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "TCP Rx in", "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_Tcp_OutSegs{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "TCP Tx out", "range": true, "refId": "B", "step": 240}], "title": "TCP In / Out", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of UDP datagrams sent and received per second, based on /proc/net/netstat", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 63}, "id": 55, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_Udp_InDatagrams{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_Udp_OutDatagrams{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP Tx out", "range": true, "refId": "B", "step": 240}], "title": "UDP In / Out", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of ICMP messages sent and received per second, including error and control messages", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 63}, "id": 115, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_Icmp_InMsgs{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "ICMP Rx in", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_Icmp_OutMsgs{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "ICMP Tx out", "range": true, "refId": "B", "step": 240}], "title": "ICMP In / Out", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks various TCP error and congestion-related events, including retransmissions, timeouts, dropped connections, and buffer issues", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 73}, "id": 104, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_TcpExt_ListenOverflows{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Listen Overflows", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_TcpExt_ListenDrops{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Listen Drops", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_TcpExt_TCPSynRetrans{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "SYN Retransmits", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_Tcp_RetransSegs{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "legendFormat": "Segment Retransmits", "range": true, "refId": "D"}, {"editorMode": "code", "expr": "irate(node_netstat_Tcp_InErrs{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "legendFormat": "Receive Errors", "range": true, "refId": "E"}, {"editorMode": "code", "expr": "irate(node_netstat_Tcp_OutRsts{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "legendFormat": "RST Sent", "range": true, "refId": "F"}, {"editorMode": "code", "expr": "irate(node_netstat_TcpExt_TCPRcvQDrop{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "Receive Queue Drops", "range": true, "refId": "G"}, {"editorMode": "code", "expr": "irate(node_netstat_TcpExt_TCPOFOQueue{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "Out-of-order Queued", "range": true, "refId": "H"}, {"editorMode": "code", "expr": "irate(node_netstat_TcpExt_TCPTimeouts{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "TCP Timeouts", "range": true, "refId": "I"}], "title": "TCP Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of UDP and UDPLite datagram delivery errors, including missing listeners, buffer overflows, and protocol-specific issues", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 73}, "id": 109, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_Udp_InErrors{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP Rx in Errors", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_Udp_NoPorts{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP No Listener", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_UdpLite_InErrors{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "interval": "", "legendFormat": "UDPLite Rx in Errors", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "irate(node_netstat_Udp_RcvbufErrors{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP Rx in Buffer Errors", "range": true, "refId": "D", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_Udp_SndbufErrors{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "UDP Tx out <PERSON><PERSON><PERSON>", "range": true, "refId": "E", "step": 240}], "title": "UDP Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of incoming ICMP messages that contained protocol-specific errors, such as bad checksums or invalid lengths", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "out (-) / in (+)", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "pps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*out.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 83}, "id": 50, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_Icmp_InErrors{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "ICMP Rx In", "range": true, "refId": "A", "step": 240}], "title": "ICMP Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of TCP SYN cookies sent, validated, and failed. These are used to protect against SYN flood attacks and manage TCP handshake resources under load", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "eps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Failed.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 83}, "id": 91, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_TcpExt_SyncookiesFailed{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "SYN Cookies Failed", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_TcpExt_SyncookiesRecv{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "SYN Cookies Validated", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_TcpExt_SyncookiesSent{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "SYN Cookies Sent", "range": true, "refId": "C", "step": 240}], "title": "TCP SynCookie", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of currently established TCP connections and the system's max supported limit. On Linux, MaxConn may return -1 to indicate a dynamic/unlimited configuration", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*<PERSON>*./"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}]}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 93}, "id": 85, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_netstat_Tcp_CurrEstab{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Current Connections", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_netstat_Tcp_MaxConn{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Max Connections", "range": true, "refId": "B", "step": 240}], "title": "TCP Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of UDP packets currently queued in the receive (RX) and transmit (TX) buffers. A growing queue may indicate a bottleneck", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 93}, "id": 337, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_udp_queues{instance=\"$node\",job=\"$job\",ip=\"v4\",queue=\"rx\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "UDP Rx in Queue", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_udp_queues{instance=\"$node\",job=\"$job\",ip=\"v4\",queue=\"tx\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "UDP Tx out <PERSON><PERSON>", "range": true, "refId": "B", "step": 240}], "title": "UDP Queue", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of TCP connection initiations per second. 'Active' opens are initiated by this host. 'Passive' opens are accepted from incoming connections", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "eps"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 103}, "id": 82, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(node_netstat_Tcp_ActiveOpens{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Active Opens", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "irate(node_netstat_Tcp_PassiveOpens{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Passive Opens", "range": true, "refId": "B", "step": 240}], "title": "TCP Direct Transition", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of TCP sockets in key connection states. Requires the --collector.tcpstat flag on node_exporter", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 103}, "id": 320, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_tcp_connection_states{state=\"established\",instance=\"$node\",job=\"$job\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Established", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "node_tcp_connection_states{state=\"fin_wait2\",instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "FIN_WAIT2", "range": true, "refId": "B", "step": 240}, {"editorMode": "code", "expr": "node_tcp_connection_states{state=\"listen\",instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Listen", "range": true, "refId": "C", "step": 240}, {"editorMode": "code", "expr": "node_tcp_connection_states{state=\"time_wait\",instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "TIME_WAIT", "range": true, "refId": "D", "step": 240}, {"editorMode": "code", "expr": "node_tcp_connection_states{state=\"close_wait\", instance=\"$node\", job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "CLOSE_WAIT", "range": true, "refId": "E", "step": 240}], "title": "TCP Stat", "type": "timeseries"}], "title": "Network Netstat", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 33}, "id": 279, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Duration of each individual collector executed during a Node Exporter scrape. Useful for identifying slow or failing collectors", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 34}, "id": 40, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_scrape_collector_duration_seconds{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{collector}}", "range": true, "refId": "A", "step": 240}], "title": "Node Exporter Scrape Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Rate of CPU time used by the process exposing this metric (user + system mode)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 34}, "id": 308, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "irate(process_cpu_seconds_total{instance=\"$node\",job=\"$job\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Process CPU Usage", "range": true, "refId": "A", "step": 240}], "title": "Exporter Process CPU Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Tracks the memory usage of the process exposing this metric (e.g., node_exporter), including current virtual memory and maximum virtual memory limit", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Virtual Memory Limit"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}, {"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Virtual Memory"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 10, "w": 10, "x": 0, "y": 44}, "id": 149, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "process_virtual_memory_bytes{instance=\"$node\",job=\"$job\"}", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Virtual Memory", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "process_virtual_memory_max_bytes{instance=\"$node\",job=\"$job\"}", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Virtual Memory Limit", "range": true, "refId": "B", "step": 240}], "title": "Exporter Processes Memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Number of file descriptors used by the exporter process versus its configured limit", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*<PERSON>*./"}, "properties": [{"id": "color", "value": {"fixedColor": "#890F02", "mode": "fixed"}}, {"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Open file descriptors"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 10, "w": 10, "x": 10, "y": 44}, "id": 64, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "process_max_fds{instance=\"$node\",job=\"$job\"}", "interval": "", "intervalFactor": 1, "legendFormat": "Maximum open file descriptors", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "process_open_fds{instance=\"$node\",job=\"$job\"}", "interval": "", "intervalFactor": 1, "legendFormat": "Open file descriptors", "range": true, "refId": "B", "step": 240}], "title": "Exporter File Descriptor Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Shows whether each Node Exporter collector scraped successfully (1 = success, 0 = failure), and whether the textfile collector returned an error.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "dark-red", "value": 0}, {"color": "green", "value": 1}]}, "unit": "bool"}, "overrides": []}, "gridPos": {"h": 10, "w": 4, "x": 20, "y": 44}, "id": 157, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.6.1", "targets": [{"editorMode": "code", "expr": "node_scrape_collector_success{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{collector}}", "range": true, "refId": "A", "step": 240}, {"editorMode": "code", "expr": "1 - node_textfile_scrape_error{instance=\"$node\",job=\"$job\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "textfile", "range": true, "refId": "B", "step": 240}], "title": "Node Exporter Scrape", "type": "bargauge"}], "title": "Node Exporter", "type": "row"}], "refresh": "1m", "schemaVersion": 41, "tags": ["linux"], "templating": {"list": [{"current": {}, "includeAll": false, "label": "Datasource", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(node_uname_info, job)", "refId": "Prometheus-job-Variable-Query"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(node_uname_info{job=\"$job\"}, nodename)", "includeAll": false, "label": "Nodename", "name": "nodename", "options": [], "query": {"query": "label_values(node_uname_info{job=\"$job\"}, nodename)", "refId": "Prometheus-nodename-Variable-Query"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(node_uname_info{job=\"$job\", nodename=\"$nodename\"}, instance)", "includeAll": false, "label": "Instance", "name": "node", "options": [], "query": {"query": "label_values(node_uname_info{job=\"$job\", nodename=\"$nodename\"}, instance)", "refId": "Prometheus-node-Variable-Query"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "[a-z]+|nvme[0-9]+n[0-9]+|mmcblk[0-9]+", "value": "[a-z]+|nvme[0-9]+n[0-9]+|mmcblk[0-9]+"}, "hide": 2, "includeAll": false, "name": "diskdevices", "options": [{"selected": true, "text": "[a-z]+|nvme[0-9]+n[0-9]+|mmcblk[0-9]+", "value": "[a-z]+|nvme[0-9]+n[0-9]+|mmcblk[0-9]+"}], "query": "[a-z]+|nvme[0-9]+n[0-9]+|mmcblk[0-9]+", "type": "custom"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Node Exporter Full", "uid": "rYdddlPWk", "version": 96, "weekStart": "", "gnetId": 1860}