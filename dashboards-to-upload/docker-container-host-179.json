{"__inputs": [{"name": "Prometheus", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "5.2.2"}, {"type": "panel", "id": "graph", "name": "Graph", "version": "5.0.0"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "5.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": "5.0.0"}, {"type": "panel", "id": "table", "name": "Table", "version": "5.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Docker Monitoring Template", "editable": true, "gnetId": 179, "graphTooltip": 1, "id": null, "iteration": 1533037835187, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 17, "panels": [], "title": "Host Info", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 1}, "id": 15, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "time() - process_start_time_seconds{job=\"prometheus\"}", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": "", "title": "Uptime", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 5, "x": 6, "y": 1}, "id": 13, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(ALERTS)", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": "0,1", "title": "<PERSON><PERSON><PERSON>", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "0"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": true, "colorValue": false, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "Prometheus", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 4, "x": 11, "y": 1}, "id": 11, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(up)", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": "0,1", "title": "Targets Online", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "Prometheus", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 4, "x": 15, "y": 1}, "id": 31, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "count(rate(container_last_seen{job=\"cadvisor\", name!=\"\"}[5m]))", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": "0,1", "title": "Running Containers", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 8}, "id": 4, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "(sum(node_memory_MemTotal_bytes) - sum(node_memory_MemFree_bytes +node_memory_Buffers_bytes + node_memory_Cached_bytes) ) / sum(node_memory_MemTotal_bytes) * 100", "format": "time_series", "interval": "10s", "intervalFactor": 1, "refId": "A", "step": 10}], "thresholds": "65, 90", "title": "Memory usage", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 8}, "id": 6, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(sum by (container_name)( rate(container_cpu_usage_seconds_total[1m] ) )) / count(node_cpu_seconds_total{mode=\"system\"}) * 100", "format": "time_series", "interval": "1m", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 10}], "thresholds": "65, 90", "title": "CPU usage", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 6, "w": 7, "x": 12, "y": 8}, "id": 7, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum (container_fs_limit_bytes - container_fs_usage_bytes) / sum(container_fs_limit_bytes)", "interval": "10s", "intervalFactor": 1, "metric": "", "refId": "A", "step": 10}], "thresholds": "65, 90", "title": "Filesystem usage", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {"RECEIVE": "#ea6460", "SENT": "#1f78c1", "TRANSMIT": "#1f78c1"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 4, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 14}, "id": 25, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{id=\"/\"}[$interval])) by (id)", "format": "time_series", "interval": "2m", "intervalFactor": 2, "legendFormat": "RECEIVE", "refId": "A"}, {"expr": "- sum(rate(container_network_transmit_bytes_total{id=\"/\"}[$interval])) by (id)", "format": "time_series", "interval": "2m", "intervalFactor": 2, "legendFormat": "TRANSMIT", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Node Network Traffic", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Available Memory": "#508642", "Used Memory": "#bf1b00"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 3, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 14}, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(node_memory_MemTotal_bytes) - sum(node_memory_MemAvailable_bytes)", "format": "time_series", "interval": "2m", "intervalFactor": 2, "legendFormat": "Used Memory", "refId": "B"}, {"expr": "sum(node_memory_MemAvailable_bytes)", "format": "time_series", "interval": "2m", "intervalFactor": 2, "legendFormat": "Available Memory", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Node Mermory", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Available Memory": "#508642", "Free Storage": "#447ebc", "Total Storage Available": "#508642", "Used Memory": "#bf1b00", "Used Storage": "#bf1b00"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 3, "gridPos": {"h": 9, "w": 7, "x": 12, "y": 14}, "id": 28, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(node_filesystem_free_bytes {job=\"node-exporter\", instance=~\".*9100\", device=~\"/dev/.*\", mountpoint!=\"/var/lib/docker/aufs\"}) ", "format": "time_series", "interval": "2m", "intervalFactor": 2, "legendFormat": "Free Storage", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Filesystem Available", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 19, "panels": [], "repeat": null, "title": "Container Performance", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 3, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 10, "w": 6, "x": 0, "y": 24}, "id": 3, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "container_cpu_load_average_10s{image!=\"\"}", "format": "time_series", "interval": "10s", "intervalFactor": 1, "legendFormat": "{{ name }}", "metric": "container_cpu_user_seconds_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Container CPU usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 10, "w": 6, "x": 6, "y": 24}, "id": 2, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "container_memory_max_usage_bytes{image!=\"\"}", "format": "time_series", "interval": "10s", "intervalFactor": 1, "legendFormat": "{{ name }}", "metric": "container_memory_usage:sort_desc", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Container Memory Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [], "datasource": "Prometheus", "fontSize": "100%", "gridPos": {"h": 13, "w": 10, "x": 12, "y": 24}, "id": 23, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "ALERTS", "format": "table", "intervalFactor": 1, "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "transform": "table", "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 14, "w": 6, "x": 0, "y": 34}, "id": 8, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sort_desc(sum by (name) (rate(container_network_receive_bytes_total{image!=\"\"}[1m] ) ))", "interval": "10s", "intervalFactor": 1, "legendFormat": "{{ name }}", "metric": "container_network_receive_bytes_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Container Network Input", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 14, "w": 6, "x": 6, "y": 34}, "id": 9, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sort_desc(sum by (name) (rate(container_network_transmit_bytes_total{image!=\"\"}[1m] ) ))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ name }}", "metric": "container_network_transmit_bytes_total", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Container Network Output", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [], "datasource": "Prometheus", "fontSize": "100%", "gridPos": {"h": 10, "w": 10, "x": 12, "y": 37}, "id": 30, "links": [], "pageSize": 10, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "dateFormat": "YYYY-MM-DD HH:mm:ss", "link": false, "linkUrl": "", "pattern": "Time", "type": "date"}, {"alias": "", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "cadvisor_version_info", "format": "table", "instant": false, "interval": "15m", "intervalFactor": 2, "legendFormat": "cAdvisor  Version: {{cadvisorVersion}}", "refId": "A"}, {"expr": "prometheus_build_info", "format": "table", "interval": "15m", "intervalFactor": 2, "legendFormat": "Prometheus Version: {{version}}", "refId": "B"}, {"expr": "node_exporter_build_info", "format": "table", "interval": "15m", "intervalFactor": 2, "legendFormat": "Node-Exporter Version: {{version}}", "refId": "C"}], "title": "Running Versions", "transform": "table", "type": "table"}], "refresh": "10s", "schemaVersion": 16, "style": "dark", "tags": ["docker", "prometheus, ", "node-exporter", "cadvisor"], "templating": {"list": [{"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"text": "1m", "value": "1m"}, "hide": 0, "label": "interval", "name": "interval", "options": [{"selected": true, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "type": "interval"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Docker and Host Monitoring w/ Prometheus", "uid": "64nrElFmk", "version": 4}