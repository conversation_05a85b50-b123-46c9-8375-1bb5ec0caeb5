#!/bin/bash

# Grafana 中文仪表板导入脚本

echo "📊 Grafana 中文仪表板导入助手"
echo "================================"

# Grafana 配置
GRAFANA_URL="http://localhost:3000"
GRAFANA_USER="admin"
GRAFANA_PASS="admin123"

# 等待 Grafana 启动
echo "⏳ 等待 Grafana 启动..."
until curl -s "$GRAFANA_URL/api/health" > /dev/null; do
    echo "   等待 Grafana 响应..."
    sleep 2
done
echo "✅ Grafana 已启动"

echo ""
echo "🎯 推荐的中文友好仪表板："
echo ""
echo "1. Node Exporter Full (ID: 1860)"
echo "   - 最受欢迎的系统监控面板"
echo "   - 完整的 CPU、内存、磁盘、网络监控"
echo "   - 超过 100万 次下载"
echo ""
echo "2. Node Exporter for Prometheus Dashboard (ID: 11074)"
echo "   - 简洁美观的系统概览"
echo "   - 适合大屏显示"
echo ""
echo "3. Docker Container & Host Metrics (ID: 179)"
echo "   - Docker 容器监控"
echo "   - 容器资源使用情况"
echo ""
echo "4. 自定义中文服务器监控面板"
echo "   - 已自动加载"
echo "   - 完全中文界面"

echo ""
read -p "选择要导入的仪表板 (1-4, 或 'all' 导入全部): " choice

case $choice in
    1)
        echo "📥 导入 Node Exporter Full (1860)..."
        DASHBOARD_ID="1860"
        ;;
    2)
        echo "📥 导入 Node Exporter Dashboard (11074)..."
        DASHBOARD_ID="11074"
        ;;
    3)
        echo "📥 导入 Docker Container Metrics (179)..."
        DASHBOARD_ID="179"
        ;;
    4)
        echo "✅ 自定义中文面板已自动加载"
        echo "🌐 访问: $GRAFANA_URL"
        echo "👤 登录: $GRAFANA_USER / $GRAFANA_PASS"
        exit 0
        ;;
    all)
        echo "📥 导入所有推荐仪表板..."
        DASHBOARD_IDS=("1860" "11074" "179")
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 导入单个仪表板的函数
import_dashboard() {
    local id=$1
    echo "   正在导入仪表板 ID: $id"
    
    # 从 grafana.com 获取仪表板 JSON
    dashboard_json=$(curl -s "https://grafana.com/api/dashboards/$id/revisions/latest/download")
    
    if [ $? -ne 0 ] || [ -z "$dashboard_json" ]; then
        echo "   ❌ 无法获取仪表板 $id"
        return 1
    fi
    
    # 准备导入数据
    import_data=$(echo "$dashboard_json" | jq '{
        dashboard: .,
        overwrite: true,
        inputs: [
            {
                name: "DS_PROMETHEUS",
                type: "datasource",
                pluginId: "prometheus",
                value: "Prometheus"
            }
        ]
    }')
    
    # 导入到 Grafana
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -u "$GRAFANA_USER:$GRAFANA_PASS" \
        -d "$import_data" \
        "$GRAFANA_URL/api/dashboards/import")
    
    if echo "$response" | jq -e '.id' > /dev/null 2>&1; then
        dashboard_title=$(echo "$response" | jq -r '.title')
        dashboard_url=$(echo "$response" | jq -r '.url')
        echo "   ✅ 成功导入: $dashboard_title"
        echo "   🔗 访问链接: $GRAFANA_URL$dashboard_url"
    else
        echo "   ❌ 导入失败: $id"
        echo "   错误信息: $(echo "$response" | jq -r '.message // "未知错误"')"
    fi
}

# 执行导入
if [ "$choice" = "all" ]; then
    for id in "${DASHBOARD_IDS[@]}"; do
        import_dashboard "$id"
        echo ""
    done
else
    import_dashboard "$DASHBOARD_ID"
fi

echo ""
echo "🎉 导入完成！"
echo ""
echo "🌐 访问 Grafana: $GRAFANA_URL"
echo "👤 登录信息: $GRAFANA_USER / $GRAFANA_PASS"
echo ""
echo "💡 使用提示："
echo "1. 登录后在左侧菜单选择 '仪表板' 查看已导入的面板"
echo "2. 点击用户头像 → '偏好设置' → 选择 '中文(简体)' 切换语言"
echo "3. 设置时区为 'Asia/Shanghai' 以显示正确时间"
echo "4. 建议设置自动刷新间隔为 30秒 或 1分钟"
