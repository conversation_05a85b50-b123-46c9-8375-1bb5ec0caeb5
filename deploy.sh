#!/bin/bash

# 多服务器监控面板部署脚本

echo "🚀 开始部署多服务器监控面板..."

# 检查 Docker 和 Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose（新版本或旧版本）
if ! docker compose version &> /dev/null && ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 确定使用哪个 Docker Compose 命令
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

echo "📦 使用 Docker Compose: $DOCKER_COMPOSE"

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p prometheus/rules
mkdir -p grafana/provisioning/datasources
mkdir -p grafana/provisioning/dashboards
mkdir -p grafana/dashboards
mkdir -p alertmanager

# 设置权限
echo "🔐 设置目录权限..."
sudo chown -R 472:472 grafana/
sudo chown -R 65534:65534 prometheus/
sudo chown -R 65534:65534 alertmanager/

# 启动服务
echo "🐳 启动 Docker 容器..."
$DOCKER_COMPOSE up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
$DOCKER_COMPOSE ps

echo ""
echo "✅ 部署完成！"
echo ""
echo "📊 访问地址："
echo "  - Grafana 面板: http://localhost:3000 (admin/admin123)"
echo "  - Prometheus: http://localhost:9090"
echo "  - AlertManager: http://localhost:9093"
echo "  - Node Exporter: http://localhost:9100"
echo "  - cAdvisor: http://localhost:8080"
echo ""
echo "📝 下一步操作："
echo "1. 在其他服务器上部署 Node Exporter："
echo "   docker-compose -f remote-node-exporter.yml up -d"
echo ""
echo "2. 修改 prometheus/prometheus.yml 中的服务器IP地址"
echo ""
echo "3. 重启 Prometheus 以加载新配置："
echo "   $DOCKER_COMPOSE restart prometheus"
echo ""
echo "4. 在 Grafana 中导入预设仪表板"
echo ""
echo "🎯 推荐的 Grafana 仪表板 ID："
echo "  - Node Exporter Full: 1860"
echo "  - Docker Container & Host Metrics: 179"
echo "  - System Overview: 11074"
