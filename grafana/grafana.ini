[server]
http_port = 3000

[security]
admin_user = admin
admin_password = admin123

[users]
allow_sign_up = false
default_theme = dark

[auth.anonymous]
enabled = false

[log]
mode = console
level = info

[paths]
data = /var/lib/grafana
logs = /var/log/grafana
plugins = /var/lib/grafana/plugins
provisioning = /etc/grafana/provisioning

[analytics]
reporting_enabled = false
check_for_updates = false

[snapshots]
external_enabled = false

[dashboards]
default_home_dashboard_path = ""

[alerting]
enabled = true

[unified_alerting]
enabled = true

# 设置默认语言为中文
[default_preferences]
theme = dark
language = zh-Hans

# 国际化设置
[feature_toggles]
enable = internationalization
