# Grafana 中文界面设置指南

## 🌏 方法一：通过 Web 界面设置（立即生效）

### 步骤：
1. **访问 Grafana**: http://localhost:3000
2. **登录账户**:
   - 用户名: `admin`
   - 密码: `admin123`

3. **进入用户设置**:
   - 点击左侧菜单底部的 **用户头像** 或 **齿轮图标**
   - 选择 **"Preferences"** (偏好设置)

4. **更改语言**:
   - 找到 **"Language"** 选项
   - 从下拉菜单中选择 **"中文(简体)"** 或 **"Chinese (Simplified)"**
   - 点击 **"Save"** (保存)

5. **刷新页面**:
   - 按 **F5** 或点击浏览器刷新按钮
   - 界面将立即切换为中文

## 🔧 方法二：配置文件设置（已自动配置）

我已经为您配置了以下设置，重启后自动生效：

### 配置内容：
```ini
[default_preferences]
theme = dark
language = zh-Hans

[feature_toggles]
enable = internationalization
```

### 环境变量：
```bash
GF_DEFAULT_PREFERENCES_LANGUAGE=zh-Hans
GF_FEATURE_TOGGLES_ENABLE=internationalization
```

## 📋 中文界面功能对照表

| 英文 | 中文 |
|------|------|
| Dashboard | 仪表板 |
| Data Sources | 数据源 |
| Alerting | 告警 |
| Configuration | 配置 |
| Server Admin | 服务器管理 |
| Preferences | 偏好设置 |
| Profile | 个人资料 |
| Change Password | 更改密码 |
| Sign Out | 退出登录 |
| Import | 导入 |
| Export | 导出 |
| Settings | 设置 |
| Query | 查询 |
| Visualization | 可视化 |
| Panel | 面板 |
| Time Range | 时间范围 |
| Refresh | 刷新 |

## 🎯 导入中文仪表板推荐

### 推荐的中文友好仪表板：

1. **Node Exporter Full (ID: 1860)**
   - 支持中文标签
   - 完整的系统监控

2. **Node Exporter for Prometheus Dashboard (ID: 11074)**
   - 简洁美观
   - 中文友好

3. **Docker Container & Host Metrics (ID: 179)**
   - 容器监控
   - 支持中文显示

### 导入步骤（中文界面）：
1. 点击 **"+"** → **"导入"**
2. 输入仪表板ID: **1860**
3. 点击 **"加载"**
4. 选择数据源: **Prometheus**
5. 点击 **"导入"**

## 🔍 验证中文设置

### 检查项目：
- [ ] 左侧菜单显示中文
- [ ] 仪表板标题显示中文
- [ ] 设置页面显示中文
- [ ] 时间选择器显示中文
- [ ] 告警信息显示中文

## 🛠️ 故障排除

### 如果中文没有生效：

1. **清除浏览器缓存**:
   - 按 `Ctrl + F5` 强制刷新
   - 或清除浏览器缓存

2. **检查用户偏好设置**:
   - 进入 "偏好设置"
   - 确认语言设置为 "中文(简体)"

3. **重启 Grafana**:
   ```bash
   docker compose restart grafana
   ```

4. **检查配置**:
   ```bash
   docker compose logs grafana | grep -i language
   ```

## 💡 小贴士

- **主题建议**: 推荐使用 "深色" 主题，更适合监控场景
- **时区设置**: 可以在偏好设置中调整时区为 "Asia/Shanghai"
- **自动刷新**: 建议设置仪表板自动刷新间隔为 30秒 或 1分钟

## 🎉 完成！

现在您的 Grafana 界面应该已经是中文了！可以开始导入仪表板并享受中文监控体验。
