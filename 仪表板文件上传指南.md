# 📁 Grafana 仪表板文件上传指南

## 🎯 已准备的仪表板文件

我已经为您下载并准备了以下仪表板 JSON 文件，位于 `dashboards-to-upload/` 文件夹：

### 📊 可上传的仪表板文件：

1. **node-exporter-full-1860.json** ⭐⭐⭐⭐⭐
   - 最受欢迎的系统监控面板
   - 完整的 CPU、内存、磁盘、网络监控
   - 文件大小: 484KB

2. **node-exporter-prometheus-11074.json** ⭐⭐⭐⭐
   - 简洁美观的系统概览
   - 适合大屏显示
   - 文件大小: 106KB

3. **docker-container-host-179.json** ⭐⭐⭐⭐
   - Docker 容器监控
   - 容器资源使用情况
   - 文件大小: 35KB

4. **中文服务器监控面板.json** ⭐⭐⭐⭐⭐
   - 自定义中文面板
   - 完全中文界面
   - 文件大小: 9KB

## 📋 文件上传步骤

### 方法一：通过文件管理器上传

1. **打开文件管理器**
   - 导航到您的工作目录: `/opt/dashboards-to-upload/`
   - 您会看到 4 个 .json 文件

2. **在 Grafana 中导入**
   - 访问: http://localhost:3000
   - 登录: admin / admin123
   - 点击左侧 "+" → "Import"
   - 选择 "Upload dashboard JSON file"

3. **拖拽或选择文件**
   - 将 JSON 文件拖拽到上传区域
   - 或点击 "Browse" 选择文件

4. **配置并导入**
   - 修改仪表板名称（可选）
   - 选择数据源: **Prometheus**
   - 点击 "Import"

### 方法二：复制文件到本地

如果您在本地环境中工作，可以将文件复制到本地：

```bash
# 复制文件到本地下载文件夹
cp /opt/dashboards-to-upload/*.json ~/Downloads/
```

## 🎯 推荐上传顺序

### 1. 首先上传：node-exporter-full-1860.json
- **原因**: 最全面的系统监控
- **包含**: 所有基础监控指标
- **适合**: 日常系统监控

### 2. 其次上传：中文服务器监控面板.json
- **原因**: 完全中文界面
- **包含**: 核心监控指标
- **适合**: 中文用户使用

### 3. 可选上传：node-exporter-prometheus-11074.json
- **原因**: 简洁美观
- **适合**: 概览监控

### 4. 可选上传：docker-container-host-179.json
- **原因**: 容器监控
- **适合**: Docker 环境

## 🔧 上传后的配置

### 数据源配置
上传时请确保：
- ✅ 数据源选择: **Prometheus**
- ✅ 数据源 URL: http://prometheus:9090
- ✅ 访问模式: Server (default)

### 仪表板设置
导入后建议设置：
- 🕐 **时间范围**: Last 1 hour
- 🔄 **自动刷新**: 30s 或 1m
- 🌏 **时区**: Asia/Shanghai
- 🎨 **主题**: Dark (深色主题)

## 📊 上传成功后的效果

### Node Exporter Full (1860) 将显示：
- 📈 CPU 使用率和负载
- 💾 内存使用情况
- 💿 磁盘使用率和 I/O
- 🌐 网络流量统计
- 🔥 系统温度（如果支持）
- ⚡ 系统进程信息

### 中文服务器监控面板将显示：
- 📊 CPU 使用率（中文标签）
- 📊 内存使用率（中文标签）
- 📊 磁盘使用率（中文标签）
- 📊 网络流量（中文标签）

## 🚨 常见问题解决

### 问题1: 文件上传失败
**解决方案**:
- 检查文件大小（应小于 10MB）
- 确认文件格式为 .json
- 尝试刷新页面后重新上传

### 问题2: 导入后没有数据
**解决方案**:
- 确认数据源选择为 "Prometheus"
- 检查 Prometheus 服务状态
- 验证 Node Exporter 正在运行

### 问题3: 面板显示错误
**解决方案**:
- 检查时间范围设置
- 确认查询语句正确
- 重新选择数据源

## 💡 使用技巧

1. **批量上传**: 可以一次上传多个文件
2. **重命名**: 上传时可以修改仪表板名称
3. **文件夹**: 可以创建文件夹来组织仪表板
4. **收藏**: 上传后记得收藏常用面板
5. **分享**: 可以生成分享链接给其他用户

## 🎉 完成后的操作

上传成功后：
1. ✅ 设置中文界面（偏好设置 → 中文简体）
2. ✅ 调整时区为 Asia/Shanghai
3. ✅ 设置自动刷新间隔
4. ✅ 收藏重要的仪表板
5. ✅ 测试所有面板是否正常显示数据

现在您可以开始上传仪表板文件了！建议从 **node-exporter-full-1860.json** 开始。
