# 多服务器监控面板

基于 Grafana + Prometheus + Node Exporter 的完整多服务器监控解决方案。

## 🌟 功能特性

- **实时监控**: CPU、内存、磁盘、网络流量等系统指标
- **多服务器支持**: 可监控多台远程服务器
- **容器监控**: 支持 Docker 容器监控
- **告警系统**: 支持邮件、Slack、企业微信等多种告警方式
- **美观界面**: Grafana 提供专业的可视化界面
- **易于部署**: 一键 Docker 部署

## 📊 监控指标

### 系统指标
- CPU 使用率和负载
- 内存使用情况
- 磁盘使用率和 I/O
- 网络流量和连接数
- 系统进程和服务状态

### 容器指标
- 容器 CPU 和内存使用
- 容器网络和存储
- 容器状态和重启次数

## 🚀 快速部署

### 1. 主监控服务器部署

```bash
# 克隆或下载项目文件
git clone <your-repo> monitoring-dashboard
cd monitoring-dashboard

# 运行部署脚本
./deploy.sh
```

### 2. 远程服务器部署

在每台需要监控的远程服务器上运行：

```bash
# 复制 remote-node-exporter.yml 到远程服务器
scp remote-node-exporter.yml user@remote-server:/path/to/

# 在远程服务器上启动 Node Exporter
docker-compose -f remote-node-exporter.yml up -d
```

### 3. 配置远程服务器

编辑 `prometheus/prometheus.yml` 文件，在 `remote-servers` 部分添加您的服务器IP：

```yaml
- job_name: 'remote-servers'
  static_configs:
    - targets: 
      - '*************:9100'  # 替换为实际IP
      - '*************:9100'  # 添加更多服务器
```

重启 Prometheus：
```bash
docker-compose restart prometheus
```

## 🎯 访问地址

- **Grafana 监控面板**: http://localhost:3000
  - 用户名: `admin`
  - 密码: `admin123`
- **Prometheus**: http://localhost:9090
- **AlertManager**: http://localhost:9093

## 📈 推荐仪表板

在 Grafana 中导入以下仪表板 ID：

1. **Node Exporter Full** (ID: 1860) - 完整的系统监控
2. **Docker Container & Host Metrics** (ID: 179) - 容器监控
3. **System Overview** (ID: 11074) - 系统概览

### 导入步骤：
1. 登录 Grafana
2. 点击 "+" → "Import"
3. 输入仪表板 ID
4. 选择 Prometheus 数据源
5. 点击 "Import"

## ⚠️ 告警配置

### 邮件告警
编辑 `alertmanager/alertmanager.yml`：

```yaml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-app-password'
```

### 告警规则
系统预设了以下告警规则：
- CPU 使用率 > 80%
- 内存使用率 > 85%
- 磁盘使用率 > 85%
- 服务器离线
- 网络流量异常

## 🔧 自定义配置

### 修改告警阈值
编辑 `prometheus/rules/server_alerts.yml` 文件。

### 添加新的监控目标
在 `prometheus/prometheus.yml` 的 `scrape_configs` 部分添加新的 job。

### 修改数据保留时间
在 `docker-compose.yml` 中修改 Prometheus 的 `--storage.tsdb.retention.time` 参数。

## 🛠️ 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 检查日志
   docker-compose logs [service-name]
   
   # 检查端口占用
   netstat -tlnp | grep :3000
   ```

2. **远程服务器连接失败**
   - 检查防火墙设置（开放 9100 端口）
   - 确认 Node Exporter 正常运行
   - 验证网络连通性

3. **数据不显示**
   - 检查 Prometheus targets 状态
   - 验证数据源配置
   - 确认时间范围设置

## 📝 维护建议

- 定期备份 Grafana 配置和仪表板
- 监控磁盘空间使用情况
- 定期更新 Docker 镜像
- 根据需要调整数据保留策略

## 🔒 安全建议

- 修改默认密码
- 使用 HTTPS（配置反向代理）
- 限制网络访问（防火墙规则）
- 定期更新组件版本
