global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-app-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    subject: '[ALERT] {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      Instance: {{ .Labels.instance }}
      Severity: {{ .Labels.severity }}
      {{ end }}

  # 可选：Slack 通知
  # slack_configs:
  # - api_url: 'YOUR_SLACK_WEBHOOK_URL'
  #   channel: '#alerts'
  #   title: 'Server Alert'
  #   text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  # 可选：企业微信通知
  # webhook_configs:
  # - url: 'YOUR_WECHAT_WEBHOOK_URL'
  #   send_resolved: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
