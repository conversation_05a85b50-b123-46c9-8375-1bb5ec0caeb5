global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 本机 Node Exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 5s

  # 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 5s

  # 远程服务器监控 - 需要在其他服务器上部署 Node Exporter
  - job_name: 'remote-servers'
    static_configs:
      # 替换为您的实际服务器IP地址
      - targets: 
        - '*************:9100'  # 服务器1
        - '*************:9100'  # 服务器2
        - '*************:9100'  # 服务器3
    scrape_interval: 5s
    metrics_path: /metrics

  # Grafana 监控
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
