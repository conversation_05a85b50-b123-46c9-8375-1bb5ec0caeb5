global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 本机 Node Exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          server_name: 'localhost'
          server_type: 'monitoring-host'
          environment: 'production'
    scrape_interval: 5s

  # 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
        labels:
          server_name: 'localhost'
          server_type: 'monitoring-host'
          environment: 'production'
    scrape_interval: 5s

  # 本机服务器监控（通过外部IP访问）
  - job_name: 'local-server'
    static_configs:
      - targets: ['************:9100']
        labels:
          server_name: 'yuxin'
          server_type: 'main-server'
          environment: 'production'
          location: 'local'
    scrape_interval: 5s
    metrics_path: /metrics

  # 远程服务器监控 - 需要在其他服务器上部署 Node Exporter
  - job_name: 'remote-servers'
    static_configs:
      # 替换为您的实际服务器IP地址
      - targets:
        - '*************:9100'  # 服务器1
        - '*************:9100'  # 服务器2
        - '*************:9100'  # 服务器3
        labels:
          server_type: 'remote-server'
          environment: 'production'
    scrape_interval: 5s
    metrics_path: /metrics

  # Grafana 监控
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
