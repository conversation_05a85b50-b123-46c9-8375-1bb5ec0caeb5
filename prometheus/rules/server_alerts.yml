groups:
- name: server_alerts
  rules:
  # CPU 使用率告警
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 2 minutes on {{ $labels.instance }}"

  # 内存使用率告警
  - alert: HighMemoryUsage
    expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage detected"
      description: "Memory usage is above 85% on {{ $labels.instance }}"

  # 磁盘使用率告警
  - alert: HighDiskUsage
    expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 85
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High disk usage detected"
      description: "Disk usage is above 85% on {{ $labels.instance }} for filesystem {{ $labels.mountpoint }}"

  # 服务器离线告警
  - alert: ServerDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Server is down"
      description: "Server {{ $labels.instance }} has been down for more than 1 minute"

  # 网络流量异常告警
  - alert: HighNetworkTraffic
    expr: rate(node_network_receive_bytes_total[5m]) > 100000000  # 100MB/s
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High network traffic detected"
      description: "Network receive traffic is above 100MB/s on {{ $labels.instance }} interface {{ $labels.device }}"
