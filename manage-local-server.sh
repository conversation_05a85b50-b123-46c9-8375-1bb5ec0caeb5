#!/bin/bash

# 本机服务器监控管理脚本

echo "🖥️  本机服务器监控管理"
echo "========================"

# 获取本机信息
LOCAL_IP=$(hostname -I | awk '{print $1}')
HOSTNAME=$(hostname)

echo "📍 本机信息："
echo "  - 主机名: $HOSTNAME"
echo "  - IP地址: $LOCAL_IP"
echo ""

# 检查监控状态
echo "🔍 检查监控状态..."
echo ""

# 检查 Node Exporter 状态
if curl -s http://localhost:9100/metrics > /dev/null; then
    echo "✅ Node Exporter (系统监控): 正常运行"
else
    echo "❌ Node Exporter (系统监控): 未运行"
fi

# 检查 cAdvisor 状态
if curl -s http://localhost:8081/metrics > /dev/null; then
    echo "✅ cAdvisor (容器监控): 正常运行"
else
    echo "❌ cAdvisor (容器监控): 未运行"
fi

# 检查 Prometheus 中的目标状态
echo ""
echo "📊 Prometheus 监控目标状态："
curl -s "http://localhost:9090/api/v1/targets" | jq -r '.data.activeTargets[] | select(.labels.job | contains("local") or contains("node") or contains("cadvisor")) | "  - \(.labels.job): \(.labels.instance) (\(.health))"'

echo ""
echo "🎯 当前监控的本机指标："
echo "  - CPU 使用率和负载"
echo "  - 内存使用情况"
echo "  - 磁盘使用率和 I/O"
echo "  - 网络流量统计"
echo "  - Docker 容器资源使用"
echo "  - 系统进程和服务状态"

echo ""
echo "🌐 访问地址："
echo "  - Grafana 监控面板: http://localhost:3000"
echo "  - Prometheus: http://localhost:9090"
echo "  - Node Exporter 指标: http://localhost:9100/metrics"
echo "  - cAdvisor 容器监控: http://localhost:8081"

echo ""
echo "💡 提示："
echo "  1. 在 Grafana 中导入仪表板 ID: 1860 (Node Exporter Full)"
echo "  2. 在 Grafana 中导入仪表板 ID: 179 (Docker Container & Host Metrics)"
echo "  3. 使用 admin/admin123 登录 Grafana"

# 可选：显示一些实时指标
echo ""
read -p "是否显示当前系统指标？(y/n): " show_metrics

if [[ $show_metrics == "y" || $show_metrics == "Y" ]]; then
    echo ""
    echo "📈 当前系统指标："
    
    # CPU 使用率
    CPU_USAGE=$(curl -s "http://localhost:9090/api/v1/query?query=100%20-%20(avg%20by%20(instance)%20(irate(node_cpu_seconds_total%7Bmode%3D%22idle%22%7D%5B5m%5D))%20*%20100)" | jq -r '.data.result[0].value[1]' 2>/dev/null)
    if [[ $CPU_USAGE != "null" && $CPU_USAGE != "" ]]; then
        printf "  - CPU 使用率: %.2f%%\n" $CPU_USAGE
    fi
    
    # 内存使用率
    MEM_USAGE=$(curl -s "http://localhost:9090/api/v1/query?query=(1%20-%20(node_memory_MemAvailable_bytes%20%2F%20node_memory_MemTotal_bytes))%20*%20100" | jq -r '.data.result[0].value[1]' 2>/dev/null)
    if [[ $MEM_USAGE != "null" && $MEM_USAGE != "" ]]; then
        printf "  - 内存使用率: %.2f%%\n" $MEM_USAGE
    fi
    
    # 磁盘使用率
    DISK_USAGE=$(curl -s "http://localhost:9090/api/v1/query?query=(1%20-%20(node_filesystem_avail_bytes%7Bfstype!%3D%22tmpfs%22%7D%20%2F%20node_filesystem_size_bytes%7Bfstype!%3D%22tmpfs%22%7D))%20*%20100" | jq -r '.data.result[0].value[1]' 2>/dev/null)
    if [[ $DISK_USAGE != "null" && $DISK_USAGE != "" ]]; then
        printf "  - 磁盘使用率: %.2f%%\n" $DISK_USAGE
    fi
    
    echo "  - 更多详细指标请访问 Grafana 面板"
fi

echo ""
echo "✨ 本机服务器监控配置完成！"
