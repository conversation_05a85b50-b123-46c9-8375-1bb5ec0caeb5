#!/bin/bash

echo "📁 Grafana 仪表板文件准备完成！"
echo "=================================="
echo ""
echo "📍 文件位置: /opt/dashboards-to-upload/"
echo ""
echo "📊 可上传的仪表板文件："
echo ""

cd /opt/dashboards-to-upload/

for file in *.json; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        case "$file" in
            *"1860"*)
                echo "⭐⭐⭐⭐⭐ $file ($size)"
                echo "   └─ Node Exporter Full - 最受欢迎的系统监控面板"
                ;;
            *"11074"*)
                echo "⭐⭐⭐⭐ $file ($size)"
                echo "   └─ Node Exporter Dashboard - 简洁美观的系统概览"
                ;;
            *"179"*)
                echo "⭐⭐⭐⭐ $file ($size)"
                echo "   └─ Docker Container Metrics - Docker容器监控"
                ;;
            *"中文"*)
                echo "⭐⭐⭐⭐⭐ $file ($size)"
                echo "   └─ 自定义中文服务器监控面板 - 完全中文界面"
                ;;
            *)
                echo "📄 $file ($size)"
                ;;
        esac
        echo ""
    fi
done

echo "🎯 推荐上传顺序："
echo "1. node-exporter-full-1860.json (最全面)"
echo "2. 中文服务器监控面板.json (中文界面)"
echo "3. node-exporter-prometheus-11074.json (简洁)"
echo "4. docker-container-host-179.json (容器监控)"
echo ""

echo "📋 上传步骤："
echo "1. 访问 Grafana: http://localhost:3000"
echo "2. 登录: admin / admin123"
echo "3. 点击左侧 '+' → 'Import'"
echo "4. 选择 'Upload dashboard JSON file'"
echo "5. 拖拽或选择上述 JSON 文件"
echo "6. 数据源选择: Prometheus"
echo "7. 点击 'Import'"
echo ""

echo "💡 提示："
echo "- 文件位于: $(pwd)"
echo "- 可以一次上传多个文件"
echo "- 上传后记得设置中文界面"
echo "- 建议设置自动刷新为30秒"
echo ""

# 检查文件完整性
echo "🔍 文件完整性检查："
for file in *.json; do
    if [ -f "$file" ]; then
        if jq empty "$file" 2>/dev/null; then
            echo "✅ $file - JSON格式正确"
        else
            echo "❌ $file - JSON格式错误"
        fi
    fi
done

echo ""
echo "🎉 所有文件已准备完成，可以开始上传了！"
