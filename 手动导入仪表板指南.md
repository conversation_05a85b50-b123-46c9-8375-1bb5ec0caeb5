# 📊 Grafana 中文仪表板手动导入指南

## 🎯 推荐仪表板列表

### 1. Node Exporter Full (ID: 1860) ⭐⭐⭐⭐⭐
- **最受欢迎**: 超过 100万 次下载
- **功能**: 完整的系统监控
- **包含**: CPU、内存、磁盘、网络、系统负载等
- **适合**: 服务器系统监控

### 2. Node Exporter for Prometheus Dashboard (ID: 11074) ⭐⭐⭐⭐
- **特点**: 简洁美观
- **功能**: 系统概览
- **适合**: 大屏显示、概览监控

### 3. Docker Container & Host Metrics (ID: 179) ⭐⭐⭐⭐
- **功能**: Docker 容器监控
- **包含**: 容器资源使用、主机指标
- **适合**: 容器化环境

## 📋 手动导入步骤

### 步骤 1: 访问 Grafana
1. 打开浏览器访问: http://localhost:3000
2. 登录账户:
   - 用户名: `admin`
   - 密码: `admin123`

### 步骤 2: 设置中文界面（可选）
1. 点击左下角 **用户头像** 或 **齿轮图标**
2. 选择 **"Preferences"** (偏好设置)
3. 在 **"Language"** 下拉菜单选择 **"中文(简体)"**
4. 点击 **"Save"** (保存)
5. 刷新页面 (F5)

### 步骤 3: 导入仪表板
1. 点击左侧菜单的 **"+"** 号
2. 选择 **"Import"** (导入)
3. 在 **"Import via grafana.com"** 输入框中输入仪表板ID
4. 点击 **"Load"** (加载)

### 步骤 4: 配置仪表板
1. **仪表板名称**: 可以修改为中文名称
2. **文件夹**: 选择 "General" 或创建新文件夹
3. **数据源配置**:
   - 找到 "Prometheus" 或类似的数据源选项
   - 选择 **"Prometheus"** (我们配置的数据源)
4. 点击 **"Import"** (导入)

## 🎯 具体导入操作

### 导入 Node Exporter Full (推荐首选)
```
1. 输入 ID: 1860
2. 点击 "Load"
3. 修改名称为: "Node Exporter 完整监控"
4. 数据源选择: Prometheus
5. 点击 "Import"
```

### 导入 Node Exporter Dashboard
```
1. 输入 ID: 11074
2. 点击 "Load"
3. 修改名称为: "Node Exporter 系统概览"
4. 数据源选择: Prometheus
5. 点击 "Import"
```

### 导入 Docker Container Metrics
```
1. 输入 ID: 179
2. 点击 "Load"
3. 修改名称为: "Docker 容器监控"
4. 数据源选择: Prometheus
5. 点击 "Import"
```

## 🔧 导入后的配置

### 时区设置
1. 进入 **"偏好设置"**
2. 设置 **"Timezone"** 为 **"Asia/Shanghai"**
3. 保存设置

### 自动刷新设置
1. 在仪表板右上角找到刷新按钮
2. 设置自动刷新间隔为 **30秒** 或 **1分钟**

### 时间范围设置
1. 点击右上角的时间选择器
2. 推荐设置: **"Last 1 hour"** (最近1小时)

## 📊 已预装的中文仪表板

我已经为您创建了一个 **"中文服务器监控面板"**，包含:
- ✅ CPU 使用率
- ✅ 内存使用率  
- ✅ 磁盘使用率
- ✅ 网络流量

这个面板会自动出现在您的仪表板列表中。

## 🎨 仪表板自定义

### 修改面板标题为中文
1. 点击面板标题 → **"Edit"** (编辑)
2. 在右侧 **"Panel options"** 中修改 **"Title"**
3. 例如: "CPU Usage" → "CPU 使用率"
4. 点击 **"Apply"** (应用)

### 修改图例为中文
1. 在编辑模式下，找到 **"Legend"** 设置
2. 修改 **"Legend format"** 或 **"Display name"**
3. 使用中文描述，如: "{{instance}} CPU使用率"

## 🚨 常见问题解决

### 问题1: 数据源找不到
**解决**: 确保选择 "Prometheus" 数据源，如果没有则需要先配置数据源

### 问题2: 没有数据显示
**解决**: 
1. 检查 Prometheus 是否正常运行
2. 确认 Node Exporter 正在收集数据
3. 检查时间范围设置

### 问题3: 导入失败
**解决**:
1. 检查网络连接
2. 尝试刷新页面后重新导入
3. 使用不同的仪表板ID

## 🎉 完成后的效果

导入成功后，您将看到:
- 📈 实时的系统监控图表
- 🎨 美观的中文界面
- 📊 详细的性能指标
- ⚡ 自动刷新的数据

## 💡 使用建议

1. **首选导入**: ID 1860 (Node Exporter Full)
2. **设置中文**: 偏好设置 → 中文(简体)
3. **调整时区**: Asia/Shanghai
4. **自动刷新**: 30秒或1分钟
5. **收藏仪表板**: 点击星号收藏常用面板

现在您可以开始手动导入仪表板了！建议从 ID 1860 开始。
